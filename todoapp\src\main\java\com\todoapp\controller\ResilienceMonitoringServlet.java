package com.todoapp.controller;

import com.todoapp.service.ResilientTodoService;
import com.todoapp.resilience.DatabaseResilienceManager;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

/**
 * Resilience Monitoring Servlet
 * Provides endpoints to monitor database resilience status,
 * circuit breaker state, and system health
 */
@WebServlet(name = "ResilienceMonitoringServlet", urlPatterns = {"/resilience/*"})
public class ResilienceMonitoringServlet extends HttpServlet {
    
    private static final Logger logger = Logger.getLogger(ResilienceMonitoringServlet.class.getName());
    private ResilientTodoService resilientTodoService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        try {
            resilientTodoService = new ResilientTodoService();
            logger.info("✅ Resilience monitoring servlet initialized");
        } catch (Exception e) {
            logger.severe("❌ Failed to initialize resilience monitoring servlet: " + e.getMessage());
            throw new ServletException("Failed to initialize resilience monitoring", e);
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        String action = request.getParameter("action");
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                handleStatusRequest(request, response);
            } else if (pathInfo.equals("/status")) {
                handleStatusRequest(request, response);
            } else if (pathInfo.equals("/health")) {
                handleHealthCheckRequest(request, response);
            } else if (pathInfo.equals("/dashboard")) {
                handleDashboardRequest(request, response);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            logger.severe("❌ Error in resilience monitoring: " + e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"error\": \"" + e.getMessage() + "\"}");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        String action = request.getParameter("action");
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        try {
            if (pathInfo != null && pathInfo.equals("/reset")) {
                handleResetCircuitBreakerRequest(request, response);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            logger.severe("❌ Error in resilience monitoring POST: " + e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"error\": \"" + e.getMessage() + "\"}");
        }
    }
    
    /**
     * Handle resilience status request
     */
    private void handleStatusRequest(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        DatabaseResilienceManager.ResilienceStatus status = resilientTodoService.getResilienceStatus();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        PrintWriter writer = response.getWriter();
        writer.write("{\n");
        writer.write("  \"timestamp\": \"" + dateFormat.format(new Date()) + "\",\n");
        writer.write("  \"circuitBreakerState\": \"" + status.getCircuitBreakerState() + "\",\n");
        writer.write("  \"consecutiveFailures\": " + status.getConsecutiveFailures() + ",\n");
        writer.write("  \"isHealthy\": " + status.isHealthy() + ",\n");
        writer.write("  \"lastFailureTime\": " + status.getLastFailureTime() + ",\n");
        writer.write("  \"lastHealthCheck\": " + status.getLastHealthCheck() + ",\n");
        
        if (status.getLastFailureTime() > 0) {
            writer.write("  \"lastFailureTimeFormatted\": \"" + 
                        dateFormat.format(new Date(status.getLastFailureTime())) + "\",\n");
        }
        
        if (status.getLastHealthCheck() > 0) {
            writer.write("  \"lastHealthCheckFormatted\": \"" + 
                        dateFormat.format(new Date(status.getLastHealthCheck())) + "\",\n");
        }
        
        writer.write("  \"status\": \"" + getOverallStatus(status) + "\"\n");
        writer.write("}");
    }
    
    /**
     * Handle health check request
     */
    private void handleHealthCheckRequest(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        boolean isHealthy = resilientTodoService.performHealthCheck();
        DatabaseResilienceManager.ResilienceStatus status = resilientTodoService.getResilienceStatus();
        
        PrintWriter writer = response.getWriter();
        writer.write("{\n");
        writer.write("  \"healthy\": " + isHealthy + ",\n");
        writer.write("  \"circuitBreakerState\": \"" + status.getCircuitBreakerState() + "\",\n");
        writer.write("  \"message\": \"" + (isHealthy ? "Database is healthy" : "Database health check failed") + "\"\n");
        writer.write("}");
        
        if (!isHealthy) {
            response.setStatus(HttpServletResponse.SC_SERVICE_UNAVAILABLE);
        }
    }
    
    /**
     * Handle circuit breaker reset request
     */
    private void handleResetCircuitBreakerRequest(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        try {
            resilientTodoService.resetCircuitBreaker();
            
            PrintWriter writer = response.getWriter();
            writer.write("{\n");
            writer.write("  \"success\": true,\n");
            writer.write("  \"message\": \"Circuit breaker reset successfully\"\n");
            writer.write("}");
            
            logger.info("🔧 Circuit breaker reset via monitoring endpoint");
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            PrintWriter writer = response.getWriter();
            writer.write("{\n");
            writer.write("  \"success\": false,\n");
            writer.write("  \"message\": \"Failed to reset circuit breaker: " + e.getMessage() + "\"\n");
            writer.write("}");
        }
    }
    
    /**
     * Handle dashboard request (HTML response)
     */
    private void handleDashboardRequest(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        
        DatabaseResilienceManager.ResilienceStatus status = resilientTodoService.getResilienceStatus();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        PrintWriter writer = response.getWriter();
        writer.write("<!DOCTYPE html>\n");
        writer.write("<html>\n");
        writer.write("<head>\n");
        writer.write("    <title>Todo App - Resilience Dashboard</title>\n");
        writer.write("    <meta charset=\"UTF-8\">\n");
        writer.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        writer.write("    <style>\n");
        writer.write("        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n");
        writer.write("        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n");
        writer.write("        .status-card { border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin: 10px 0; }\n");
        writer.write("        .status-healthy { border-left: 4px solid #28a745; }\n");
        writer.write("        .status-warning { border-left: 4px solid #ffc107; }\n");
        writer.write("        .status-error { border-left: 4px solid #dc3545; }\n");
        writer.write("        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }\n");
        writer.write("        .btn-primary { background-color: #007bff; color: white; }\n");
        writer.write("        .btn-danger { background-color: #dc3545; color: white; }\n");
        writer.write("        .metric { display: inline-block; margin: 10px 20px 10px 0; }\n");
        writer.write("        .metric-label { font-weight: bold; color: #666; }\n");
        writer.write("        .metric-value { font-size: 1.2em; color: #333; }\n");
        writer.write("    </style>\n");
        writer.write("</head>\n");
        writer.write("<body>\n");
        writer.write("    <div class=\"container\">\n");
        writer.write("        <h1>🛡️ Todo App - Resilience Dashboard</h1>\n");
        
        // Overall status card
        String statusClass = getStatusClass(status);
        writer.write("        <div class=\"status-card " + statusClass + "\">\n");
        writer.write("            <h2>Overall Status: " + getOverallStatus(status) + "</h2>\n");
        writer.write("            <p>Last updated: " + dateFormat.format(new Date()) + "</p>\n");
        writer.write("        </div>\n");
        
        // Circuit breaker status
        writer.write("        <div class=\"status-card\">\n");
        writer.write("            <h3>🔌 Circuit Breaker</h3>\n");
        writer.write("            <div class=\"metric\">\n");
        writer.write("                <div class=\"metric-label\">State:</div>\n");
        writer.write("                <div class=\"metric-value\">" + status.getCircuitBreakerState() + "</div>\n");
        writer.write("            </div>\n");
        writer.write("            <div class=\"metric\">\n");
        writer.write("                <div class=\"metric-label\">Consecutive Failures:</div>\n");
        writer.write("                <div class=\"metric-value\">" + status.getConsecutiveFailures() + "</div>\n");
        writer.write("            </div>\n");
        
        if (status.getCircuitBreakerState() != DatabaseResilienceManager.CircuitBreakerState.CLOSED) {
            writer.write("            <button class=\"btn btn-danger\" onclick=\"resetCircuitBreaker()\">Reset Circuit Breaker</button>\n");
        }
        
        writer.write("        </div>\n");
        
        // Database health
        writer.write("        <div class=\"status-card\">\n");
        writer.write("            <h3>💾 Database Health</h3>\n");
        writer.write("            <div class=\"metric\">\n");
        writer.write("                <div class=\"metric-label\">Status:</div>\n");
        writer.write("                <div class=\"metric-value\">" + (status.isHealthy() ? "✅ Healthy" : "❌ Unhealthy") + "</div>\n");
        writer.write("            </div>\n");
        
        if (status.getLastHealthCheck() > 0) {
            writer.write("            <div class=\"metric\">\n");
            writer.write("                <div class=\"metric-label\">Last Health Check:</div>\n");
            writer.write("                <div class=\"metric-value\">" + dateFormat.format(new Date(status.getLastHealthCheck())) + "</div>\n");
            writer.write("            </div>\n");
        }
        
        writer.write("            <button class=\"btn btn-primary\" onclick=\"performHealthCheck()\">Perform Health Check</button>\n");
        writer.write("        </div>\n");
        
        // Failure information
        if (status.getLastFailureTime() > 0) {
            writer.write("        <div class=\"status-card status-warning\">\n");
            writer.write("            <h3>⚠️ Last Failure</h3>\n");
            writer.write("            <div class=\"metric\">\n");
            writer.write("                <div class=\"metric-label\">Time:</div>\n");
            writer.write("                <div class=\"metric-value\">" + dateFormat.format(new Date(status.getLastFailureTime())) + "</div>\n");
            writer.write("            </div>\n");
            writer.write("        </div>\n");
        }
        
        // Actions
        writer.write("        <div class=\"status-card\">\n");
        writer.write("            <h3>🔧 Actions</h3>\n");
        writer.write("            <button class=\"btn btn-primary\" onclick=\"refreshStatus()\">Refresh Status</button>\n");
        writer.write("            <button class=\"btn btn-primary\" onclick=\"viewJsonStatus()\">View JSON Status</button>\n");
        writer.write("        </div>\n");
        
        writer.write("    </div>\n");
        
        // JavaScript
        writer.write("    <script>\n");
        writer.write("        function refreshStatus() {\n");
        writer.write("            window.location.reload();\n");
        writer.write("        }\n");
        writer.write("        function viewJsonStatus() {\n");
        writer.write("            window.open('/todo-list-app/resilience/status', '_blank');\n");
        writer.write("        }\n");
        writer.write("        function performHealthCheck() {\n");
        writer.write("            fetch('/todo-list-app/resilience/health')\n");
        writer.write("                .then(response => response.json())\n");
        writer.write("                .then(data => {\n");
        writer.write("                    alert('Health check result: ' + (data.healthy ? 'Healthy' : 'Unhealthy'));\n");
        writer.write("                    refreshStatus();\n");
        writer.write("                })\n");
        writer.write("                .catch(error => alert('Health check failed: ' + error));\n");
        writer.write("        }\n");
        writer.write("        function resetCircuitBreaker() {\n");
        writer.write("            if (confirm('Are you sure you want to reset the circuit breaker?')) {\n");
        writer.write("                fetch('/todo-list-app/resilience/reset', { method: 'POST' })\n");
        writer.write("                    .then(response => response.json())\n");
        writer.write("                    .then(data => {\n");
        writer.write("                        alert(data.message);\n");
        writer.write("                        refreshStatus();\n");
        writer.write("                    })\n");
        writer.write("                    .catch(error => alert('Reset failed: ' + error));\n");
        writer.write("            }\n");
        writer.write("        }\n");
        writer.write("    </script>\n");
        writer.write("</body>\n");
        writer.write("</html>\n");
    }
    
    /**
     * Get overall status string
     */
    private String getOverallStatus(DatabaseResilienceManager.ResilienceStatus status) {
        if (status.getCircuitBreakerState() == DatabaseResilienceManager.CircuitBreakerState.OPEN) {
            return "CIRCUIT_BREAKER_OPEN";
        } else if (!status.isHealthy()) {
            return "UNHEALTHY";
        } else if (status.getCircuitBreakerState() == DatabaseResilienceManager.CircuitBreakerState.HALF_OPEN) {
            return "RECOVERING";
        } else {
            return "HEALTHY";
        }
    }
    
    /**
     * Get CSS class for status
     */
    private String getStatusClass(DatabaseResilienceManager.ResilienceStatus status) {
        String overallStatus = getOverallStatus(status);
        switch (overallStatus) {
            case "HEALTHY":
                return "status-healthy";
            case "RECOVERING":
                return "status-warning";
            case "UNHEALTHY":
            case "CIRCUIT_BREAKER_OPEN":
                return "status-error";
            default:
                return "";
        }
    }
}
