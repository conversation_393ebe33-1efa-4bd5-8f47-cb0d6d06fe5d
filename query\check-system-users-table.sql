-- Connect to system schema
CONNECT system/SavvySpend123@//localhost:1521/XEPDB1;

-- Check SYSTEM schema users table structure
SELECT 'SYSTEM schema USERS table structure:' AS info FROM dual;
DESCRIBE system.users;

-- Check what's in SYSTEM users table
SELECT 'Data in SYSTEM.USERS table:' AS info FROM dual;
SELECT * FROM system.users WHERE ROWNUM <= 10;

-- Look for demo user in SYSTEM schema
SELECT 'Looking for demo user in SYSTEM schema:' AS info FROM dual;
SELECT COUNT(*) AS demo_count FROM system.users WHERE username = 'demo';

-- If demo user exists, show their details
SELECT * FROM system.users WHERE username = 'demo';

-- Check for password-related columns in SYSTEM.users
SELECT 'Password-related columns in SYSTEM.users:' AS info FROM dual;
SELECT column_name, data_type, data_length
FROM all_tab_columns 
WHERE table_name = 'USERS' 
AND owner = 'SYSTEM'
AND (UPPER(column_name) LIKE '%PASSWORD%' OR UPPER(column_name) LIKE '%HASH%')
ORDER BY column_id;

-- Show all columns in SYSTEM.users table
SELECT 'All columns in SYSTEM.users:' AS info FROM dual;
SELECT column_name, data_type, data_length, nullable
FROM all_tab_columns 
WHERE table_name = 'USERS' 
AND owner = 'SYSTEM'
ORDER BY column_id;

EXIT;
