-- =============================================================================
-- SAVVYSPEND APPLICATION - DATABASE SCHEMA
-- =============================================================================
-- Creates the complete database schema for SavvySpend Spring Boot application
-- Includes: users, categories, expenses, budgets, goals tables
-- =============================================================================

SET SERVEROUTPUT ON SIZE 1000000
SET ECHO ON

PROMPT =============================================================================;
PROMPT CREATING SAVVYSPEND APPLICATION SCHEMA
PROMPT =============================================================================;

-- Connect to the pluggable database
ALTER SESSION SET CONTAINER = XEPDB1;

-- Check if savvyspend user already exists, create only if not exists
DECLARE
    user_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM dba_users WHERE username = 'SAVVYSPEND';
    
    IF user_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE USER savvyspend IDENTIFIED BY SavvySpend123 DEFAULT TABLESPACE USERS TEMPORARY TABLESPACE TEMP';
        EXECUTE IMMEDIATE 'GRANT CONNECT, RESOURCE TO savvyspend';
        EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO savvyspend';
        EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO savvyspend';
        EXECUTE IMMEDIATE 'GRANT CREATE SEQUENCE TO savvyspend';
        EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO savvyspend';
        EXECUTE IMMEDIATE 'ALTER USER savvyspend QUOTA UNLIMITED ON USERS';
        DBMS_OUTPUT.PUT_LINE('✅ User savvyspend created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  User savvyspend already exists, skipping creation');
    END IF;
END;
/

-- Connect as the savvyspend user
CONNECT savvyspend/SavvySpend123@XEPDB1;

-- Create users table
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'USERS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE users (
            id NUMBER(19) NOT NULL,
            username VARCHAR2(50) NOT NULL UNIQUE,
            password VARCHAR2(255) NOT NULL,
            email VARCHAR2(100) NOT NULL UNIQUE,
            first_name VARCHAR2(50),
            last_name VARCHAR2(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active NUMBER(1) DEFAULT 1,
            CONSTRAINT pk_users PRIMARY KEY (id),
            CONSTRAINT chk_users_active CHECK (active IN (0, 1))
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table users created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table users already exists, skipping creation');
    END IF;
END;
/

-- Create categories table
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'CATEGORIES';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE categories (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            description VARCHAR2(255),
            color VARCHAR2(7) DEFAULT ''#007bff'',
            user_id NUMBER(19) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT pk_categories PRIMARY KEY (id),
            CONSTRAINT fk_categories_user FOREIGN KEY (user_id) REFERENCES users(id)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table categories created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table categories already exists, skipping creation');
    END IF;
END;
/

-- Create expenses table
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'EXPENSES';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE expenses (
            id NUMBER(19) NOT NULL,
            amount NUMBER(10,2) NOT NULL,
            description VARCHAR2(255) NOT NULL,
            expense_date DATE NOT NULL,
            category_id NUMBER(19) NOT NULL,
            user_id NUMBER(19) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT pk_expenses PRIMARY KEY (id),
            CONSTRAINT fk_expenses_category FOREIGN KEY (category_id) REFERENCES categories(id),
            CONSTRAINT fk_expenses_user FOREIGN KEY (user_id) REFERENCES users(id),
            CONSTRAINT chk_expenses_amount CHECK (amount > 0)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table expenses created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table expenses already exists, skipping creation');
    END IF;
END;
/

-- Create budgets table (THIS IS THE MISSING TABLE CAUSING THE ERROR)
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'BUDGETS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE budgets (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            amount NUMBER(10,2) NOT NULL,
            spent NUMBER(10,2) DEFAULT 0,
            category_id NUMBER(19) NOT NULL,
            user_id NUMBER(19) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active NUMBER(1) DEFAULT 1,
            CONSTRAINT pk_budgets PRIMARY KEY (id),
            CONSTRAINT fk_budgets_category FOREIGN KEY (category_id) REFERENCES categories(id),
            CONSTRAINT fk_budgets_user FOREIGN KEY (user_id) REFERENCES users(id),
            CONSTRAINT chk_budgets_amount CHECK (amount > 0),
            CONSTRAINT chk_budgets_spent CHECK (spent >= 0),
            CONSTRAINT chk_budgets_active CHECK (active IN (0, 1)),
            CONSTRAINT chk_budgets_dates CHECK (end_date > start_date)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table budgets created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table budgets already exists, skipping creation');
    END IF;
END;
/

-- Create sequences for auto-incrementing IDs
DECLARE
    seq_count NUMBER;
BEGIN
    -- Users sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'USERS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE users_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence users_seq created');
    END IF;

    -- Categories sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'CATEGORIES_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE categories_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence categories_seq created');
    END IF;

    -- Expenses sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'EXPENSES_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE expenses_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence expenses_seq created');
    END IF;

    -- Budgets sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'BUDGETS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE budgets_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence budgets_seq created');
    END IF;

    -- Goals sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'GOALS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE goals_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence goals_seq created');
    END IF;
END;
/

-- Create triggers for auto-incrementing IDs and updating timestamps
CREATE OR REPLACE TRIGGER trg_users_id
    BEFORE INSERT ON users
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := users_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_categories_id
    BEFORE INSERT ON categories
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := categories_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_expenses_id
    BEFORE INSERT ON expenses
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := expenses_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_expenses_updated_at
    BEFORE UPDATE ON expenses
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_budgets_id
    BEFORE INSERT ON budgets
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := budgets_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_budgets_updated_at
    BEFORE UPDATE ON budgets
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_goals_id
    BEFORE INSERT ON goals
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := goals_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_goals_updated_at
    BEFORE UPDATE ON goals
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- Create indexes for better performance
DECLARE
    idx_count NUMBER;
BEGIN
    -- Users indexes
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_USERS_USERNAME';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_users_username ON users(username)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_users_username created');
    END IF;

    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_USERS_EMAIL';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_users_email ON users(email)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_users_email created');
    END IF;

    -- Categories indexes
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_CATEGORIES_USER_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_categories_user_id ON categories(user_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_categories_user_id created');
    END IF;

    -- Expenses indexes
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_EXPENSES_USER_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_expenses_user_id ON expenses(user_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_expenses_user_id created');
    END IF;

    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_EXPENSES_CATEGORY_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_expenses_category_id ON expenses(category_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_expenses_category_id created');
    END IF;

    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_EXPENSES_DATE';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_expenses_date ON expenses(expense_date)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_expenses_date created');
    END IF;

    -- Budgets indexes
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_BUDGETS_USER_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_budgets_user_id ON budgets(user_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_budgets_user_id created');
    END IF;

    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_BUDGETS_CATEGORY_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_budgets_category_id ON budgets(category_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_budgets_category_id created');
    END IF;

    -- Goals indexes
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_GOALS_USER_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_goals_user_id ON goals(user_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_goals_user_id created');
    END IF;
END;
/

-- Insert sample data only if tables are empty
DECLARE
    data_count NUMBER;
BEGIN
    -- Check if users table is empty
    SELECT COUNT(*) INTO data_count FROM users;

    IF data_count = 0 THEN
        -- Insert sample users
        INSERT INTO users (username, password, email, first_name, last_name) VALUES
        ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIFi', '<EMAIL>', 'Admin', 'User');

        INSERT INTO users (username, password, email, first_name, last_name) VALUES
        ('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIFi', '<EMAIL>', 'Test', 'User');

        -- Insert sample categories
        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Food & Dining', 'Restaurants, groceries, and food expenses', '#FF6B6B', 1);

        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Transportation', 'Gas, public transport, car maintenance', '#4ECDC4', 1);

        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Entertainment', 'Movies, games, hobbies', '#45B7D1', 1);

        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Utilities', 'Electricity, water, internet, phone', '#FFA07A', 1);

        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Healthcare', 'Medical expenses, insurance, pharmacy', '#98D8C8', 1);

        -- Insert sample expenses
        INSERT INTO expenses (amount, description, expense_date, category_id, user_id) VALUES
        (25.50, 'Lunch at restaurant', SYSDATE - 1, 1, 1);

        INSERT INTO expenses (amount, description, expense_date, category_id, user_id) VALUES
        (45.00, 'Gas for car', SYSDATE - 2, 2, 1);

        INSERT INTO expenses (amount, description, expense_date, category_id, user_id) VALUES
        (15.99, 'Movie ticket', SYSDATE - 3, 3, 1);

        INSERT INTO expenses (amount, description, expense_date, category_id, user_id) VALUES
        (120.00, 'Electricity bill', SYSDATE - 5, 4, 1);

        -- Insert sample budgets (THIS IS THE KEY TABLE THAT WAS MISSING)
        INSERT INTO budgets (name, amount, spent, category_id, user_id, start_date, end_date) VALUES
        ('Monthly Food Budget', 500.00, 125.50, 1, 1, TRUNC(SYSDATE, 'MM'), LAST_DAY(SYSDATE));

        INSERT INTO budgets (name, amount, spent, category_id, user_id, start_date, end_date) VALUES
        ('Transportation Budget', 200.00, 45.00, 2, 1, TRUNC(SYSDATE, 'MM'), LAST_DAY(SYSDATE));

        INSERT INTO budgets (name, amount, spent, category_id, user_id, start_date, end_date) VALUES
        ('Entertainment Budget', 100.00, 15.99, 3, 1, TRUNC(SYSDATE, 'MM'), LAST_DAY(SYSDATE));

        -- Insert sample goals
        INSERT INTO goals (name, target_amount, current_amount, target_date, user_id) VALUES
        ('Emergency Fund', 5000.00, 1250.00, ADD_MONTHS(SYSDATE, 12), 1);

        INSERT INTO goals (name, target_amount, current_amount, target_date, user_id) VALUES
        ('Vacation Fund', 2000.00, 350.00, ADD_MONTHS(SYSDATE, 6), 1);

        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ Sample data inserted successfully');
        DBMS_OUTPUT.PUT_LINE('   - 2 users created');
        DBMS_OUTPUT.PUT_LINE('   - 5 categories created');
        DBMS_OUTPUT.PUT_LINE('   - 4 expenses created');
        DBMS_OUTPUT.PUT_LINE('   - 3 budgets created (including the missing budgets table!)');
        DBMS_OUTPUT.PUT_LINE('   - 2 goals created');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Sample data already exists (' || data_count || ' users), skipping insertion');
    END IF;
END;
/

PROMPT;
PROMPT =============================================================================;
PROMPT SAVVYSPEND APPLICATION SCHEMA CREATED SUCCESSFULLY!;
PROMPT =============================================================================;
PROMPT;
PROMPT Tables Created:;
PROMPT - users (with authentication data);
PROMPT - categories (expense categories);
PROMPT - expenses (expense tracking);
PROMPT - budgets (budget management) ← THIS WAS THE MISSING TABLE!;
PROMPT - goals (financial goals);
PROMPT;
PROMPT Sample Data:;
PROMPT - 2 users (admin/testuser, password: password);
PROMPT - 5 categories (Food, Transportation, Entertainment, Utilities, Healthcare);
PROMPT - 4 sample expenses;
PROMPT - 3 sample budgets;
PROMPT - 2 financial goals;
PROMPT;
PROMPT The SavvySpend Spring Boot application should now start successfully!;
PROMPT =============================================================================;

-- Create goals table
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'GOALS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE goals (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            target_amount NUMBER(10,2) NOT NULL,
            current_amount NUMBER(10,2) DEFAULT 0,
            target_date DATE,
            user_id NUMBER(19) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            achieved NUMBER(1) DEFAULT 0,
            CONSTRAINT pk_goals PRIMARY KEY (id),
            CONSTRAINT fk_goals_user FOREIGN KEY (user_id) REFERENCES users(id),
            CONSTRAINT chk_goals_target_amount CHECK (target_amount > 0),
            CONSTRAINT chk_goals_current_amount CHECK (current_amount >= 0),
            CONSTRAINT chk_goals_achieved CHECK (achieved IN (0, 1))
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table goals created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table goals already exists, skipping creation');
    END IF;
END;
/
