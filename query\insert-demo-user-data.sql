-- Insert Demo User Data for SavvySpend Application
-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Enable output for debugging
SET SERVEROUTPUT ON;
SET ECHO ON;

-- Check if demo user already exists
DECLARE
    user_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users WHERE username = 'demo';
    IF user_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('Demo user already exists. Deleting existing demo user...');
        DELETE FROM users WHERE username = 'demo';
        COMMIT;
    END IF;
END;
/

-- Insert demo user with comprehensive data
INSERT INTO users (
    user_id,
    id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active,
    preferred_currency,
    created_date,
    created_at,
    updated_at
) VALUES (
    user_seq.NEXTVAL,                                                    -- user_id (auto-generated)
    999,                                                                 -- id (unique identifier)
    'demo',                                                              -- username
    '<EMAIL>',                                              -- email
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqIcxkZDHKUYpY/Jo8.W6wHbBZWO2',   -- password_hash (password: 'password123')
    'Demo',                                                              -- first_name
    'User',                                                              -- last_name
    'USER',                                                              -- role
    1,                                                                   -- is_active (1 = true)
    'USD',                                                               -- preferred_currency
    SYSDATE,                                                             -- created_date
    SYSDATE,                                                             -- created_at
    SYSDATE                                                              -- updated_at
);

-- Insert additional demo users for testing
INSERT INTO users (
    user_id,
    id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active,
    preferred_currency,
    created_date,
    created_at,
    updated_at
) VALUES (
    user_seq.NEXTVAL,
    1000,
    'testuser1',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqIcxkZDHKUYpY/Jo8.W6wHbBZWO2',
    'Test',
    'User One',
    'USER',
    1,
    'USD',
    SYSDATE,
    SYSDATE,
    SYSDATE
);

INSERT INTO users (
    user_id,
    id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active,
    preferred_currency,
    created_date,
    created_at,
    updated_at
) VALUES (
    user_seq.NEXTVAL,
    1001,
    'testuser2',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqIcxkZDHKUYpY/Jo8.W6wHbBZWO2',
    'Test',
    'User Two',
    'USER',
    1,
    'EUR',
    SYSDATE,
    SYSDATE,
    SYSDATE
);

INSERT INTO users (
    user_id,
    id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active,
    preferred_currency,
    created_date,
    created_at,
    updated_at
) VALUES (
    user_seq.NEXTVAL,
    1002,
    'admin_demo',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqIcxkZDHKUYpY/Jo8.W6wHbBZWO2',
    'Admin',
    'User',
    'ADMIN',
    1,
    'USD',
    SYSDATE,
    SYSDATE,
    SYSDATE
);

-- Commit the changes
COMMIT;

-- Verify the insertion
SELECT 'Demo users inserted successfully!' AS status FROM dual;

-- Display inserted users
SELECT 
    user_id,
    id,
    username,
    email,
    first_name,
    last_name,
    role,
    is_active,
    preferred_currency,
    TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS') AS created_at
FROM users 
WHERE username IN ('demo', 'testuser1', 'testuser2', 'admin_demo')
ORDER BY user_id;

-- Show total user count
SELECT 'Total users in database: ' || COUNT(*) AS user_count FROM users;

EXIT;
