# =============================================================================
# SAVVYSPEND APPLICATION - JNDI CONFIGURATION
# =============================================================================
# This configuration uses JNDI instead of direct JDBC connection
# Requires WebSphere JNDI DataSource: jdbc/SavvySpendDS
# =============================================================================

# Application Configuration
spring.application.name=SavvySpend Pro
server.servlet.context-path=/savvyspend

# =============================================================================
# DATABASE CONFIGURATION - JNDI (WebSphere)
# =============================================================================

# JNDI DataSource Configuration (ENABLED) - Use existing JNDI
spring.datasource.jndi-name=jdbc/OracleDS

# Remove direct JDBC configuration (DISABLED)
# spring.datasource.url=***********************************
# spring.datasource.username=system
# spring.datasource.password=SavvySpend123
# spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# =============================================================================
# JPA/HIBERNATE CONFIGURATION
# =============================================================================

# Hibernate Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Connection Pool Settings (managed by WebSphere)
spring.jpa.properties.hibernate.connection.provider_disables_autocommit=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Application Logging
logging.level.com.savvyspend=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# Root logging level
logging.level.root=INFO

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================

# Session timeout
server.servlet.session.timeout=30m
server.servlet.session.cookie.max-age=1800

# =============================================================================
# WEBSPHERE SPECIFIC CONFIGURATION
# =============================================================================

# Disable Spring Boot's embedded server features for WebSphere
spring.main.web-application-type=servlet

# Disable Spring Boot DevTools in production
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# =============================================================================
# APPLICATION FEATURES
# =============================================================================

# Disable demo data initialization (use real database data)
app.demo.enabled=false
app.init.demo-data=false

# Enable real data features
app.database.real-data=true
app.features.reports=true
app.features.budgets=true
app.features.goals=true

# =============================================================================
# JNDI DATASOURCE REQUIREMENTS
# =============================================================================
# 
# This configuration requires the following JNDI DataSource in WebSphere:
#
# DataSource Name: SavvySpendDataSource
# JNDI Name: jdbc/SavvySpendDS
# Database URL: ***********************************
# Username: system
# Password: SavvySpend123
#
# Required Tables in SYSTEM schema:
# - BUDGETS (id, name, amount, spent, category_id, user_id, start_date, end_date, created_at, updated_at, active)
# - USERS (id, username, password, email, first_name, last_name, created_at, updated_at, active)
# - CATEGORIES (id, name, description, color, user_id, created_at)
# - EXPENSES (id, amount, description, expense_date, category_id, user_id, created_at, updated_at)
# - GOALS (id, name, target_amount, current_amount, target_date, user_id, created_at, updated_at, achieved)
#
# =============================================================================
