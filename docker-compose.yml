services:
  # Oracle Database
  oracle-db:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    container_name: oracle
    ports:
      - "1521:1521"
      - "5500:5500"
    environment:
      - ORACLE_PDB=XEPDB1
      - ORACLE_PWD=SavvySpend123
      - ORACLE_CHARACTERSET=AL32UTF8
      - TZ=Asia/Kolkata
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./database/init:/opt/oracle/scripts/startup
    restart: unless-stopped
    networks:
      - webapp-network
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "system/SavvySpend123@//localhost:1521/XE", "@/opt/oracle/scripts/startup/healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # WebSphere Application Server
  websphere:
    image: "ibmcom/websphere-traditional:latest"
    container_name: appserver
    ports:
      - "9080:9080"   # WebSphere HTTP port
      - "9043:9043"   # WebSphere Admin Console (HTTPS)
    environment:
      # Admin user configuration - IBM's official method
      - ENABLE_BASIC_LOGGING=true
      #- ADMIN_USER_NAME=wsadmin
      #- ADMIN_PASSWORD=was@123

      # Time zone configuration
      - TZ=Asia/Kolkata
      - JAVA_TIMEZONE=Asia/Kolkata

      # Profile configuration
      - PROFILE_NAME=AppSrv01
      - SERVER_NAME=server1
      - CELL_NAME=DefaultCell01
      - NODE_NAME=DefaultNode01

      # Oracle Database Connection
      #- ORACLE_HOST=oracle-db
      #- ORACLE_PORT=1521
      #- ORACLE_SID=XE
      #- ORACLE_PDB=XEPDB1
      #- ORACLE_USER=savvyspend
      #- ORACLE_PASSWORD=SavvySpend123

    volumes:
      # Use named volumes for WebSphere persistence
      - websphere_profiles:/opt/IBM/WebSphere/AppServer/profiles

      # WAR files for deployment (copy from original)
      #- ./websphere/deployedapp/webapp2.war:/tmp/webapp2.war

      # Oracle JDBC JARs - Added for database connectivity
      - ./websphere/jdbc/ojdbc8.jar:/opt/IBM/WebSphere/AppServer/lib/ojdbc8.jar:ro
      - ./websphere/jdbc/orai18n.jar:/opt/IBM/WebSphere/AppServer/java/8.0/jre/lib/ext/orai18n.jar:ro

    restart: always
    depends_on:
      - oracle-db
    networks:
      - webapp-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9080/ || exit 1"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 300s

  # HAProxy Load Balancer
  haproxy:
    build:
      context: ./haproxy
      dockerfile: Dockerfile
    image: custom-haproxy:latest
    container_name: haproxy
    environment:
      - TZ=Asia/Kolkata
    ports:
      - "443:443"     # HTTPS port
    volumes:
      - ./haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    depends_on:
      - websphere
    restart: always
    networks:
      - webapp-network

  # ActiveMQ Message Broker
  activemq:
    image: apache/activemq-classic:5.18.3
    container_name: activemq
    environment:
      - TZ=Asia/Kolkata
      - ACTIVEMQ_ADMIN_LOGIN=admin
      - ACTIVEMQ_ADMIN_PASSCODE=admin123
    ports:
      - "61616:61616"   # JMS port
      - "8161:8161"     # Web console port
    volumes:
      - activemq_data:/data/activemq
      - activemq_logs:/var/log/activemq
    restart: always
    networks:
      - webapp-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8161/admin/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Nginx Web Server (Frontend)
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    image: custom-nginx:latest
    container_name: nginx
    environment:
      - TZ=Asia/Kolkata
    ports:
      - "80:80"       # Main HTTP entry point
    volumes:
      - ./nginx/html:/usr/share/nginx/html:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - haproxy
    restart: always
    networks:
      - webapp-network

networks:
  webapp-network:
    driver: bridge
    
volumes:
  websphere_profiles:
    driver: local
  oracle_data:
    driver: local
  activemq_data:
    driver: local
  activemq_logs:
    driver: local
