#!/usr/bin/env python
# Configure JNDI DataSource for SavvySpend Application in WebSphere

print("=== Configuring SavvySpend JNDI DataSource in WebSphere ===")

try:
    # Configuration parameters for SavvySpend
    datasourceName = "SavvySpendDataSource"
    jndiName = "jdbc/SavvySpendDS"
    jdbcProviderName = "Oracle JDBC Provider for SavvySpend"
    
    # Oracle connection details - connecting to XE container database where we created the tables
    serverName = "oracle-db"
    portNumber = "1521"
    databaseName = "XE"  # Container database, not XEPDB1
    user = "system"      # System user where we created the budgets table
    password = "SavvySpend123"
    
    print("Creating Oracle JDBC Provider for SavvySpend...")
    
    # Get the server scope
    server = AdminConfig.getid('/Server:server1/')
    print("Server scope: " + str(server))
    
    if not server:
        print("❌ Error: Could not find server1")
        exit(1)
    
    # Create JDBC Provider
    jdbcProvider = AdminConfig.create('J<PERSON><PERSON><PERSON>rovider', server,
        [['name', jdbcProviderName],
         ['description', 'Oracle JDBC Provider for SavvySpend Application'],
         ['classpath', '/opt/IBM/WebSphere/AppServer/lib/ojdbc8.jar'],
         ['implementationClassName', 'oracle.jdbc.pool.OracleConnectionPoolDataSource'],
         ['providerType', 'Oracle JDBC Driver']])
    
    print("✅ JDBC Provider created: " + str(jdbcProvider))
    
    # Create DataSource
    print("Creating SavvySpend DataSource...")
    
    dataSource = AdminConfig.create('DataSource', jdbcProvider,
        [['name', datasourceName],
         ['description', 'Oracle DataSource for SavvySpend Application - XE Container Database'],
         ['jndiName', jndiName],
         ['datasourceHelperClassname', 'com.ibm.websphere.rsadapter.Oracle11gDataStoreHelper'],
         ['statementCacheSize', 10],
         ['authDataAlias', ''],
         ['authMechanismPreference', 'BASIC_PASSWORD']])
    
    print("✅ DataSource created: " + str(dataSource))
    
    # Configure connection properties
    print("Configuring connection properties...")
    
    # Create property set
    propertySet = AdminConfig.create('J2EEResourcePropertySet', dataSource, [])
    
    # Database URL - pointing to XE container database
    urlProperty = AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'URL'],
         ['type', 'java.lang.String'],
         ['value', '***********************************'],
         ['description', 'Oracle database URL for XE container database']])
    
    # Username
    userProperty = AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'user'],
         ['type', 'java.lang.String'],
         ['value', user],
         ['description', 'Database username']])
    
    # Password
    passwordProperty = AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'password'],
         ['type', 'java.lang.String'],
         ['value', password],
         ['description', 'Database password']])
    
    print("✅ Connection properties configured")
    
    # Configure connection pool
    print("Configuring connection pool...")
    
    connectionPool = AdminConfig.create('ConnectionPool', dataSource,
        [['minConnections', 5],
         ['maxConnections', 20],
         ['connectionTimeout', 30],
         ['reapTime', 180],
         ['unusedTimeout', 1800],
         ['agedTimeout', 0],
         ['purgePolicy', 'EntirePool'],
         ['numberOfSharedPoolPartitions', 0],
         ['numberOfUnsharedPoolPartitions', 0],
         ['numberOfFreePoolPartitions', 0],
         ['freePoolDistributionTableSize', 0],
         ['surgeThreshold', -1],
         ['surgeCreationInterval', 0]])
    
    print("✅ Connection pool configured")
    
    # Save the configuration
    print("Saving configuration...")
    AdminConfig.save()
    print("✅ Configuration saved successfully")
    
    print("")
    print("=== SavvySpend JNDI Configuration Summary ===")
    print("✅ JDBC Provider: " + jdbcProviderName)
    print("✅ DataSource: " + datasourceName)
    print("✅ JNDI Name: " + jndiName)
    print("✅ Database URL: ***********************************")
    print("✅ Database User: " + user)
    print("✅ Connection Pool: 5-20 connections")
    print("")
    print("🔄 Please restart the WebSphere server to apply changes:")
    print("   docker restart appserver")
    print("")
    print("📝 Update your SavvySpend application to use JNDI:")
    print("   Remove: spring.datasource.url, spring.datasource.username, spring.datasource.password")
    print("   Add: spring.datasource.jndi-name=jdbc/SavvySpendDS")
    
except Exception as e:
    print("❌ Error occurred: " + str(e))
    import traceback
    traceback.print_exc()
    exit(1)

print("=== SavvySpend JNDI Configuration Complete ===")
