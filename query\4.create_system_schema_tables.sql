-- =============================================================================
-- SAVVYSPEND APPLICATION - SYSTEM SCHEMA TABLES
-- =============================================================================
-- Creates the SavvySpend tables in the SYSTEM schema for immediate compatibility
-- This is a quick fix - the application is configured to connect to SYSTEM schema
-- =============================================================================

SET SERVEROUTPUT ON SIZE 1000000
SET ECHO ON

PROMPT =============================================================================;
PROMPT CREATING SAVVYSPEND TABLES IN SYSTEM SCHEMA
PROMPT =============================================================================;

-- Connect to the pluggable database as SYSTEM user
ALTER SESSION SET CONTAINER = XEPDB1;
CONNECT system/SavvySpend123@XEPDB1;

-- Create users table in SYSTEM schema
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'USERS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE users (
            id NUMBER(19) NOT NULL,
            username VARCHAR2(50) NOT NULL UNIQUE,
            password VARCHAR2(255) NOT NULL,
            email VARCHAR2(100) NOT NULL UNIQUE,
            first_name VARCHAR2(50),
            last_name VARCHAR2(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active NUMBER(1) DEFAULT 1,
            CONSTRAINT pk_users PRIMARY KEY (id),
            CONSTRAINT chk_users_active CHECK (active IN (0, 1))
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table users created in SYSTEM schema');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table users already exists in SYSTEM schema');
    END IF;
END;
/

-- Create categories table in SYSTEM schema
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'CATEGORIES';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE categories (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            description VARCHAR2(255),
            color VARCHAR2(7) DEFAULT ''#007bff'',
            user_id NUMBER(19) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT pk_categories PRIMARY KEY (id),
            CONSTRAINT fk_categories_user FOREIGN KEY (user_id) REFERENCES users(id)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table categories created in SYSTEM schema');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table categories already exists in SYSTEM schema');
    END IF;
END;
/

-- Create expenses table in SYSTEM schema
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'EXPENSES';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE expenses (
            id NUMBER(19) NOT NULL,
            amount NUMBER(10,2) NOT NULL,
            description VARCHAR2(255) NOT NULL,
            expense_date DATE NOT NULL,
            category_id NUMBER(19) NOT NULL,
            user_id NUMBER(19) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT pk_expenses PRIMARY KEY (id),
            CONSTRAINT fk_expenses_category FOREIGN KEY (category_id) REFERENCES categories(id),
            CONSTRAINT fk_expenses_user FOREIGN KEY (user_id) REFERENCES users(id),
            CONSTRAINT chk_expenses_amount CHECK (amount > 0)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table expenses created in SYSTEM schema');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table expenses already exists in SYSTEM schema');
    END IF;
END;
/

-- Create budgets table in SYSTEM schema (THE MISSING TABLE!)
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'BUDGETS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE budgets (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            amount NUMBER(10,2) NOT NULL,
            spent NUMBER(10,2) DEFAULT 0,
            category_id NUMBER(19) NOT NULL,
            user_id NUMBER(19) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active NUMBER(1) DEFAULT 1,
            CONSTRAINT pk_budgets PRIMARY KEY (id),
            CONSTRAINT fk_budgets_category FOREIGN KEY (category_id) REFERENCES categories(id),
            CONSTRAINT fk_budgets_user FOREIGN KEY (user_id) REFERENCES users(id),
            CONSTRAINT chk_budgets_amount CHECK (amount > 0),
            CONSTRAINT chk_budgets_spent CHECK (spent >= 0),
            CONSTRAINT chk_budgets_active CHECK (active IN (0, 1)),
            CONSTRAINT chk_budgets_dates CHECK (end_date > start_date)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table budgets created in SYSTEM schema - THIS FIXES THE ERROR!');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table budgets already exists in SYSTEM schema');
    END IF;
END;
/

-- Create goals table in SYSTEM schema
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'GOALS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE goals (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            target_amount NUMBER(10,2) NOT NULL,
            current_amount NUMBER(10,2) DEFAULT 0,
            target_date DATE,
            user_id NUMBER(19) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            achieved NUMBER(1) DEFAULT 0,
            CONSTRAINT pk_goals PRIMARY KEY (id),
            CONSTRAINT fk_goals_user FOREIGN KEY (user_id) REFERENCES users(id),
            CONSTRAINT chk_goals_target_amount CHECK (target_amount > 0),
            CONSTRAINT chk_goals_current_amount CHECK (current_amount >= 0),
            CONSTRAINT chk_goals_achieved CHECK (achieved IN (0, 1))
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table goals created in SYSTEM schema');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table goals already exists in SYSTEM schema');
    END IF;
END;
/

-- Create sequences for auto-incrementing IDs
DECLARE
    seq_count NUMBER;
BEGIN
    -- Users sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'USERS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE users_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence users_seq created in SYSTEM schema');
    END IF;

    -- Categories sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'CATEGORIES_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE categories_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence categories_seq created in SYSTEM schema');
    END IF;

    -- Expenses sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'EXPENSES_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE expenses_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence expenses_seq created in SYSTEM schema');
    END IF;

    -- Budgets sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'BUDGETS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE budgets_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence budgets_seq created in SYSTEM schema');
    END IF;

    -- Goals sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'GOALS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE goals_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence goals_seq created in SYSTEM schema');
    END IF;
END;
/

-- Create triggers for auto-incrementing IDs
CREATE OR REPLACE TRIGGER trg_users_id
    BEFORE INSERT ON users
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := users_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_categories_id
    BEFORE INSERT ON categories
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := categories_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_expenses_id
    BEFORE INSERT ON expenses
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := expenses_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_budgets_id
    BEFORE INSERT ON budgets
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := budgets_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_goals_id
    BEFORE INSERT ON goals
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := goals_seq.NEXTVAL;
    END IF;
END;
/

-- Insert sample data
DECLARE
    data_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO data_count FROM users;

    IF data_count = 0 THEN
        -- Insert sample users
        INSERT INTO users (username, password, email, first_name, last_name) VALUES
        ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIFi', '<EMAIL>', 'Admin', 'User');

        INSERT INTO users (username, password, email, first_name, last_name) VALUES
        ('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIFi', '<EMAIL>', 'Test', 'User');

        -- Insert sample categories
        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Food & Dining', 'Restaurants, groceries, and food expenses', '#FF6B6B', 1);

        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Transportation', 'Gas, public transport, car maintenance', '#4ECDC4', 1);

        INSERT INTO categories (name, description, color, user_id) VALUES
        ('Entertainment', 'Movies, games, hobbies', '#45B7D1', 1);

        -- Insert sample budgets (THE KEY TABLE!)
        INSERT INTO budgets (name, amount, spent, category_id, user_id, start_date, end_date) VALUES
        ('Monthly Food Budget', 500.00, 125.50, 1, 1, TRUNC(SYSDATE, 'MM'), LAST_DAY(SYSDATE));

        INSERT INTO budgets (name, amount, spent, category_id, user_id, start_date, end_date) VALUES
        ('Transportation Budget', 200.00, 45.00, 2, 1, TRUNC(SYSDATE, 'MM'), LAST_DAY(SYSDATE));

        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ Sample data inserted in SYSTEM schema');
        DBMS_OUTPUT.PUT_LINE('   - 2 users created');
        DBMS_OUTPUT.PUT_LINE('   - 3 categories created');
        DBMS_OUTPUT.PUT_LINE('   - 2 budgets created (FIXES THE MISSING TABLE ERROR!)');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Sample data already exists in SYSTEM schema');
    END IF;
END;
/

PROMPT;
PROMPT =============================================================================;
PROMPT SAVVYSPEND TABLES CREATED IN SYSTEM SCHEMA - APPLICATION SHOULD NOW START!;
PROMPT =============================================================================;
