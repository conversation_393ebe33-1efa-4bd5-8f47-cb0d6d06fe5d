# HAProxy Configuration for WebSphere Load Balancing
global
    # Log to stdout instead of /dev/log to avoid file system issues
    log stdout local0 notice
    maxconn 2000
    # Disable daemon mode to keep container running
    daemon

defaults
    log global
    mode http
    option httplog
    option dontlognull
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

frontend websphere_frontend
    bind *:80       # H<PERSON>roxy listens on port 80 for HTTP traffic
    bind *:443      # HAProxy listens on port 443 for HTTPS traffic (for SSL offloading later)
    mode http
    default_backend websphere_backend

backend websphere_backend
    mode http
    balance roundrobin # Load balancing algorithm (e.g., roundrobin, leastconn, source)
    # Define your WebSphere server. Use the container name as the hostname.
    # The port should be the *internal* HTTP port of the WebSphere container.
    server websphere1 appserver:9080 check
    # If you have more WebSphere app servers on the same network, add them here:
    # server websphere2 websphere-appserver2:9080 check
    # server websphere3 websphere-appserver3:9080 check
    # server websphere4 websphere-appserver4:9080 check

