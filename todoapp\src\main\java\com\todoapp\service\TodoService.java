package com.todoapp.service;

import com.todoapp.entity.Todo;
import com.todoapp.messaging.TodoMessagingService;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.inject.Inject;
import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * TodoService - Business logic for Todo operations
 * Uses JNDI DataSource for database connectivity (WebSphere compatible)
 */
public class TodoService {
    private static final Logger logger = Logger.getLogger(TodoService.class.getName());
    private static final String JNDI_NAME = "jdbc/OracleDS";

    private DataSource dataSource;
    private TodoMessagingService messagingService;
    private boolean asyncProcessingEnabled = true;

    public TodoService() {
        try {
            Context ctx = new InitialContext();
            dataSource = (DataSource) ctx.lookup(JNDI_NAME);
            logger.info("TodoService initialized with JNDI DataSource: " + JNDI_NAME);

            // Initialize messaging service for async processing
            try {
                messagingService = new TodoMessagingService();
                logger.info("✅ Messaging service initialized for async processing");
            } catch (Exception me) {
                logger.log(Level.WARNING, "⚠️ Failed to initialize messaging service, continuing without async processing", me);
                asyncProcessingEnabled = false;
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to initialize TodoService with JNDI DataSource", e);
            throw new RuntimeException("Database connection failed", e);
        }
    }

    /**
     * Get all todos for a user
     */
    public List<Todo> getAllTodos(Long userId) {
        List<Todo> todos = new ArrayList<>();
        String sql = "SELECT * FROM todos WHERE user_id = ? ORDER BY created_date DESC";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, userId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                todos.add(mapResultSetToTodo(rs));
            }
            
            logger.info("Retrieved " + todos.size() + " todos for user " + userId);
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error retrieving todos", e);
            throw new RuntimeException("Failed to retrieve todos", e);
        }
        
        return todos;
    }

    /**
     * Get todos by status
     */
    public List<Todo> getTodosByStatus(Long userId, boolean completed) {
        List<Todo> todos = new ArrayList<>();
        String sql = "SELECT * FROM todos WHERE user_id = ? AND completed = ? ORDER BY created_date DESC";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, userId);
            stmt.setInt(2, completed ? 1 : 0);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                todos.add(mapResultSetToTodo(rs));
            }
            
            logger.info("Retrieved " + todos.size() + " " + (completed ? "completed" : "pending") + " todos for user " + userId);
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error retrieving todos by status", e);
            throw new RuntimeException("Failed to retrieve todos", e);
        }
        
        return todos;
    }

    /**
     * Get a single todo by ID
     */
    public Todo getTodoById(Long todoId) {
        String sql = "SELECT * FROM todos WHERE todo_id = ?";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, todoId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToTodo(rs);
            }
            
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error retrieving todo by ID: " + todoId, e);
            throw new RuntimeException("Failed to retrieve todo", e);
        }
        
        return null;
    }

    /**
     * Create a new todo
     */
    public Todo createTodo(Todo todo) {
        String sql = "INSERT INTO todos (title, description, priority, due_date, user_id) VALUES (?, ?, ?, ?, ?)";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, new String[]{"todo_id"})) {
            
            stmt.setString(1, todo.getTitle());
            stmt.setString(2, todo.getDescription());
            stmt.setString(3, todo.getPriority());
            stmt.setDate(4, todo.getDueDate());
            stmt.setLong(5, todo.getUserId());
            
            int rowsAffected = stmt.executeUpdate();
            
            if (rowsAffected > 0) {
                ResultSet generatedKeys = stmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    todo.setTodoId(generatedKeys.getLong(1));
                }
                logger.info("Created new todo with ID: " + todo.getTodoId());

                Todo createdTodo = getTodoById(todo.getTodoId());

                // Send async notification if messaging is enabled
                if (asyncProcessingEnabled && messagingService != null) {
                    try {
                        messagingService.sendCreateTodoAsync(createdTodo, String.valueOf(todo.getUserId()));
                    } catch (Exception e) {
                        logger.log(Level.WARNING, "Failed to send async create notification", e);
                        // Continue - don't fail the operation due to messaging issues
                    }
                }

                return createdTodo;
            }
            
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error creating todo", e);
            throw new RuntimeException("Failed to create todo", e);
        }
        
        return null;
    }

    /**
     * Update an existing todo
     */
    public Todo updateTodo(Todo todo) {
        String sql = "UPDATE todos SET title = ?, description = ?, priority = ?, due_date = ?, completed = ? WHERE todo_id = ?";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, todo.getTitle());
            stmt.setString(2, todo.getDescription());
            stmt.setString(3, todo.getPriority());
            stmt.setDate(4, todo.getDueDate());
            stmt.setInt(5, todo.isCompleted() ? 1 : 0);
            stmt.setLong(6, todo.getTodoId());
            
            int rowsAffected = stmt.executeUpdate();
            
            if (rowsAffected > 0) {
                logger.info("Updated todo with ID: " + todo.getTodoId());

                Todo updatedTodo = getTodoById(todo.getTodoId());

                // Send async notification if messaging is enabled
                if (asyncProcessingEnabled && messagingService != null) {
                    try {
                        messagingService.sendUpdateTodoAsync(updatedTodo, String.valueOf(todo.getUserId()));
                    } catch (Exception e) {
                        logger.log(Level.WARNING, "Failed to send async update notification", e);
                        // Continue - don't fail the operation due to messaging issues
                    }
                }

                return updatedTodo;
            }
            
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error updating todo", e);
            throw new RuntimeException("Failed to update todo", e);
        }
        
        return null;
    }

    /**
     * Toggle todo completion status
     */
    public boolean toggleTodoStatus(Long todoId) {
        String sql = "UPDATE todos SET completed = CASE WHEN completed = 0 THEN 1 ELSE 0 END WHERE todo_id = ?";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, todoId);
            int rowsAffected = stmt.executeUpdate();
            
            if (rowsAffected > 0) {
                logger.info("Toggled status for todo ID: " + todoId);

                // Send async notification if messaging is enabled
                if (asyncProcessingEnabled && messagingService != null) {
                    try {
                        // Get the todo to determine user ID
                        Todo todo = getTodoById(todoId);
                        if (todo != null) {
                            messagingService.sendToggleTodoAsync(todoId, String.valueOf(todo.getUserId()));
                        }
                    } catch (Exception e) {
                        logger.log(Level.WARNING, "Failed to send async toggle notification", e);
                        // Continue - don't fail the operation due to messaging issues
                    }
                }

                return true;
            }
            
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error toggling todo status", e);
            throw new RuntimeException("Failed to toggle todo status", e);
        }
        
        return false;
    }

    /**
     * Delete a todo
     */
    public boolean deleteTodo(Long todoId) {
        // Get the todo before deletion for async notification
        Todo todoToDelete = null;
        if (asyncProcessingEnabled && messagingService != null) {
            todoToDelete = getTodoById(todoId);
        }

        String sql = "DELETE FROM todos WHERE todo_id = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, todoId);
            int rowsAffected = stmt.executeUpdate();

            if (rowsAffected > 0) {
                logger.info("Deleted todo with ID: " + todoId);

                // Send async notification if messaging is enabled
                if (asyncProcessingEnabled && messagingService != null && todoToDelete != null) {
                    try {
                        messagingService.sendDeleteTodoAsync(todoId, String.valueOf(todoToDelete.getUserId()));
                    } catch (Exception e) {
                        logger.log(Level.WARNING, "Failed to send async delete notification", e);
                        // Continue - don't fail the operation due to messaging issues
                    }
                }

                return true;
            }
            
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error deleting todo", e);
            throw new RuntimeException("Failed to delete todo", e);
        }
        
        return false;
    }

    /**
     * Get todo statistics
     */
    public TodoStats getTodoStats(Long userId) {
        String sql = "SELECT " +
                     "COUNT(*) as total, " +
                     "SUM(CASE WHEN completed = 1 THEN 1 ELSE 0 END) as completed, " +
                     "SUM(CASE WHEN completed = 0 THEN 1 ELSE 0 END) as pending, " +
                     "SUM(CASE WHEN completed = 0 AND due_date < SYSDATE THEN 1 ELSE 0 END) as overdue " +
                     "FROM todos WHERE user_id = ?";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, userId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return new TodoStats(
                    rs.getInt("total"),
                    rs.getInt("completed"),
                    rs.getInt("pending"),
                    rs.getInt("overdue")
                );
            }
            
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error retrieving todo statistics", e);
        }
        
        return new TodoStats(0, 0, 0, 0);
    }

    /**
     * Map ResultSet to Todo object
     */
    private Todo mapResultSetToTodo(ResultSet rs) throws SQLException {
        Todo todo = new Todo();
        todo.setTodoId(rs.getLong("todo_id"));
        todo.setTitle(rs.getString("title"));
        todo.setDescription(rs.getString("description"));
        todo.setCompleted(rs.getInt("completed") == 1);
        todo.setPriority(rs.getString("priority"));
        todo.setCreatedDate(rs.getTimestamp("created_date"));
        todo.setUpdatedDate(rs.getTimestamp("updated_date"));
        todo.setDueDate(rs.getDate("due_date"));
        todo.setUserId(rs.getLong("user_id"));
        return todo;
    }

    /**
     * Inner class for todo statistics
     */
    public static class TodoStats {
        private final int total;
        private final int completed;
        private final int pending;
        private final int overdue;

        public TodoStats(int total, int completed, int pending, int overdue) {
            this.total = total;
            this.completed = completed;
            this.pending = pending;
            this.overdue = overdue;
        }

        public int getTotal() { return total; }
        public int getCompleted() { return completed; }
        public int getPending() { return pending; }
        public int getOverdue() { return overdue; }
        
        public double getCompletionPercentage() {
            return total > 0 ? (double) completed / total * 100 : 0;
        }
    }
}
