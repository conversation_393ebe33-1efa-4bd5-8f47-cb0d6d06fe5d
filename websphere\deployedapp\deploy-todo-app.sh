#!/bin/bash

# =============================================================================
# Todo List Application - Deployment Script for WebSphere
# =============================================================================
# This script deploys the Todo List application to IBM WebSphere Application Server
# =============================================================================

set -e  # Exit on any error

echo "============================================================================="
echo "Deploying Todo List Application to WebSphere"
echo "============================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
WAR_FILE="todo-list-app.war"
APP_NAME="TodoListApp"
CONTEXT_ROOT="/todo-list-app"
WEBSPHERE_CONTAINER="appserver"

# Check if WAR file exists
if [ ! -f "$WAR_FILE" ]; then
    print_error "WAR file not found: $WAR_FILE"
    print_error "Please build the application first using: cd ../todoapp && ./build.sh"
    exit 1
fi

print_status "Found WAR file: $WAR_FILE"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running"
    print_error "Please start Docker and try again"
    exit 1
fi

# Check if WebSphere container is running
if ! docker ps | grep -q "$WEBSPHERE_CONTAINER"; then
    print_error "WebSphere container '$WEBSPHERE_CONTAINER' is not running"
    print_error "Please start the WebSphere container using: docker-compose up -d websphere"
    exit 1
fi

print_status "WebSphere container is running"

# Copy WAR file to WebSphere container
print_status "Copying WAR file to WebSphere container..."
docker cp "$WAR_FILE" "$WEBSPHERE_CONTAINER:/tmp/$WAR_FILE"

if [ $? -eq 0 ]; then
    print_success "WAR file copied to container"
else
    print_error "Failed to copy WAR file to container"
    exit 1
fi

# Deploy application using wsadmin
print_status "Deploying application using wsadmin..."

# Create deployment script
DEPLOY_SCRIPT="/tmp/deploy_todo_app.py"
cat > deploy_script.py << 'EOF'
#!/usr/bin/env python
# Deploy Todo List Application

print("=== Deploying Todo List Application ===")

try:
    # Application details
    appName = "TodoListApp"
    warFile = "/tmp/todo-list-app.war"
    contextRoot = "/todo-list-app"
    
    print("Application Name: " + appName)
    print("WAR File: " + warFile)
    print("Context Root: " + contextRoot)
    
    # Check if application already exists
    apps = AdminApp.list()
    if appName in apps:
        print("Application already exists. Uninstalling...")
        AdminApp.uninstall(appName)
        AdminConfig.save()
        print("✅ Application uninstalled")
    
    # Install application
    print("Installing application...")
    AdminApp.install(warFile, [
        '-appname', appName,
        '-contextroot', contextRoot,
        '-MapWebModToVH', [['.*', '.*', 'default_host']],
        '-MapModulesToServers', [['.*', '.*', 'WebSphere:cell=DefaultCell01,node=DefaultNode01,server=server1']],
        '-usedefaultbindings'
    ])
    
    print("✅ Application installed")
    
    # Save configuration
    print("Saving configuration...")
    AdminConfig.save()
    print("✅ Configuration saved")
    
    # Start application
    print("Starting application...")
    appManager = AdminControl.queryNames('type=ApplicationManager,*')
    if appManager:
        AdminControl.invoke(appManager, 'startApplication', appName)
        print("✅ Application started")
    else:
        print("⚠️  Could not start application automatically")
        print("   Please start it manually from the WebSphere Admin Console")
    
    print("\n=== Deployment Complete ===")
    print("Application Name: " + appName)
    print("Context Root: " + contextRoot)
    print("Access URL: http://localhost:9080" + contextRoot + "/todos")
    print("\nTo verify deployment:")
    print("1. Check WebSphere Admin Console")
    print("2. Access the application URL")
    print("3. Check application logs")
    
except Exception as e:
    print("❌ Deployment failed: " + str(e))
    import traceback
    traceback.print_exc()
EOF

# Copy deployment script to container
docker cp deploy_script.py "$WEBSPHERE_CONTAINER:$DEPLOY_SCRIPT"

# Execute deployment script
print_status "Executing deployment script..."
docker exec -it "$WEBSPHERE_CONTAINER" /opt/IBM/WebSphere/AppServer/bin/wsadmin.sh -lang jython -f "$DEPLOY_SCRIPT"

if [ $? -eq 0 ]; then
    print_success "Application deployed successfully"
else
    print_error "Deployment failed"
    exit 1
fi

# Cleanup
print_status "Cleaning up temporary files..."
docker exec "$WEBSPHERE_CONTAINER" rm -f "$DEPLOY_SCRIPT" "/tmp/$WAR_FILE"
rm -f deploy_script.py

# Wait a moment for application to start
print_status "Waiting for application to start..."
sleep 10

# Test application
print_status "Testing application..."
if curl -f -s "http://localhost:9080/todo-list-app/todos" > /dev/null; then
    print_success "Application is responding"
else
    print_warning "Application may not be fully started yet"
    print_warning "Please wait a few more seconds and try accessing the URL manually"
fi

echo ""
echo "============================================================================="
print_success "Deployment completed!"
echo "============================================================================="
echo ""
print_status "Application Details:"
echo "• Name: TodoListApp"
echo "• Context Root: /todo-list-app"
echo "• Access URL: http://localhost:9080/todo-list-app/todos"
echo ""
print_status "Next Steps:"
echo "1. Ensure Oracle database is running"
echo "2. Run database setup: docker exec -it oracle sqlplus system/SavvySpend123@XE @/opt/oracle/scripts/startup/create_todos_table.sql"
echo "3. Access the application at the URL above"
echo ""
print_status "Troubleshooting:"
echo "• Check WebSphere logs: docker logs appserver"
echo "• Access Admin Console: http://localhost:9043/ibm/console"
echo "• Check application status in Admin Console"
echo ""
echo "============================================================================="
