#!/usr/bin/env python
# Restart Todo List Application

print("=== Restarting Todo List Application ===")

try:
    appName = "TodoListApp"
    
    # Stop application
    print("Stopping application...")
    appManager = AdminControl.queryNames('type=ApplicationManager,*')
    if appManager:
        AdminControl.invoke(appManager, 'stopApplication', appName)
        print("✅ Application stopped")
        
        # Wait a moment
        import time
        time.sleep(3)
        
        # Start application
        print("Starting application...")
        AdminControl.invoke(appManager, 'startApplication', appName)
        print("✅ Application started")
    else:
        print("⚠️  Could not find ApplicationManager")
    
    print("\n=== Restart Complete ===")
    print("Access URL: http://localhost:9080/todo-list-app/todos")
    
except Exception as e:
    print("❌ Restart failed: " + str(e))
    import traceback
    traceback.print_exc()
