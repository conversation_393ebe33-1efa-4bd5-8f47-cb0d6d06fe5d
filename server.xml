<?xml version="1.0" encoding="UTF-8"?>
<process:Server xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:adminservice="http://www.ibm.com/websphere/appserver/schemas/5.0/adminservice.xmi" xmlns:applicationserver="http://www.ibm.com/websphere/appserver/schemas/5.0/applicationserver.xmi" xmlns:applicationserver.ejbcontainer="http://www.ibm.com/websphere/appserver/schemas/5.0/applicationserver.ejbcontainer.xmi" xmlns:applicationserver.ejbcontainer.messagelistener="http://www.ibm.com/websphere/appserver/schemas/5.0/applicationserver.ejbcontainer.messagelistener.xmi" xmlns:applicationserver.sipcontainer="www.ibm.com/websphere/appserver/schemas/7.0/applicationserver.sipcontainer.xmi" xmlns:applicationserver.webcontainer="http://www.ibm.com/websphere/appserver/schemas/5.0/applicationserver.webcontainer.xmi" xmlns:channelservice="http://www.ibm.com/websphere/appserver/schemas/6.0/channelservice.xmi" xmlns:channelservice.channels="http://www.ibm.com/websphere/appserver/schemas/6.0/channelservice.channels.xmi" xmlns:coregroupbridgeservice="http://www.ibm.com/websphere/appserver/schemas/6.0/coregroupbridgeservice.xmi" xmlns:diagnosticproviderservice="http://www.ibm.com/websphere/appserver/schemas/5.0/diagnosticproviderservice.xmi" xmlns:loggingservice.http="http://www.ibm.com/websphere/appserver/schemas/6.0/loggingservice.http.xmi" xmlns:loggingservice.ras="http://www.ibm.com/websphere/appserver/schemas/5.0/loggingservice.ras.xmi" xmlns:namingserver="http://www.ibm.com/websphere/appserver/schemas/5.0/namingserver.xmi" xmlns:orb="http://www.ibm.com/websphere/appserver/schemas/5.0/orb.xmi" xmlns:pmiservice="http://www.ibm.com/websphere/appserver/schemas/5.0/pmiservice.xmi" xmlns:portletcontainer="http://www.ibm.com/websphere/appserver/schemas/6.0/portletcontainer.xmi" xmlns:process="http://www.ibm.com/websphere/appserver/schemas/5.0/process.xmi" xmlns:processexec="http://www.ibm.com/websphere/appserver/schemas/5.0/processexec.xmi" xmlns:sibresources="http://www.ibm.com/websphere/appserver/schemas/6.0/sibresources.xmi" xmlns:threadpoolmanager="http://www.ibm.com/websphere/appserver/schemas/6.0/threadpoolmanager.xmi" xmlns:tperfviewer="http://www.ibm.com/websphere/appserver/schemas/6.0/tperfviewer.xmi" xmlns:traceservice="http://www.ibm.com/websphere/appserver/schemas/5.0/traceservice.xmi" xmi:id="Server_1183122130078" name="server1">
  <stateManagement xmi:id="StateManageable_1183122130078" initialState="START"/>
  <statisticsProvider xmi:id="StatisticsProvider_1183122130078" specification="com.ibm.orb=enabled"/>
  <services xmi:type="pmiservice:PMIService" xmi:id="PMIService_1183122130078" enable="true" initialSpecLevel="" statisticSet="basic" synchronizedUpdate="false"/>
  <services xmi:type="adminservice:AdminService" xmi:id="AdminService_1183122130078" enable="true" standalone="true" localAdminProtocol="IPCConnector_1" remoteAdminProtocol="SOAPConnector_1">
    <connectors xmi:type="adminservice:SOAPConnector" xmi:id="SOAPConnector_1" enable="true">
      <properties xmi:id="Property_3" name="requestTimeout" value="600"/>
    </connectors>
    <connectors xmi:type="adminservice:RMIConnector" xmi:id="RMIConnector_1" enable="true"/>
    <connectors xmi:type="adminservice:JSR160RMIConnector" xmi:id="JSR160RMIConnector_1" enable="true"/>
    <connectors xmi:type="adminservice:IPCConnector" xmi:id="IPCConnector_1" enable="true">
      <properties xmi:id="Property_4" name="requestTimeout" value="600"/>
    </connectors>
    <configRepository xmi:id="RepositoryService_1183122130078"/>
    <pluginConfigService xmi:id="PluginConfigService_1183122130078" enable="true"/>
  </services>
  <services xmi:type="traceservice:TraceService" xmi:id="TraceService_1183122130078" enable="true" startupTraceSpecification="*=info" traceOutputType="SPECIFIED_FILE" traceFormat="BASIC" memoryBufferSize="8">
    <traceLog xmi:id="TraceLog_1183122130078" fileName="${SERVER_LOG_ROOT}/trace.log" rolloverSize="20" maxNumberOfBackupFiles="5"/>
  </services>
  <services xmi:type="diagnosticproviderservice:DiagnosticProviderService" xmi:id="DiagnosticProviderService_1183122130078" enable="true" startupStateCollectionSpec=".*:.*=0"/>
  <services xmi:type="loggingservice.ras:RASLoggingService" xmi:id="RASLoggingService_1183122130078" enable="true" messageFilterLevel="INFO" enableCorrelationId="true">
    <serviceLog xmi:id="ServiceLog_1183122130078" name="${LOG_ROOT}/activity.log" size="2" enabled="true"/>
  </services>
  <services xmi:type="coregroupbridgeservice:CoreGroupBridgeService" xmi:id="CoreGroupBridgeService_1183122130078" enable="true"/>
  <services xmi:type="tperfviewer:TPVService" xmi:id="TPVService_1183122130078" enable="true"/>
  <services xmi:type="orb:ObjectRequestBroker" xmi:id="ObjectRequestBroker_1183122130078" enable="true" requestTimeout="180" requestRetriesCount="1" requestRetriesDelay="0" connectionCacheMaximum="240" connectionCacheMinimum="100" commTraceEnabled="false" locateRequestTimeout="180" forceTunnel="never" noLocalCopies="false" useServerThreadPool="false">
    <properties xmi:id="Property_1183122130079" name="com.ibm.CORBA.enableLocateRequest" value="true"/>
    <properties xmi:id="Property_1183122130080" name="com.ibm.ws.orb.transport.WSSSLServerSocketFactoryName" value="com.ibm.ws.security.orbssl.WSSSLServerSocketFactoryImpl"/>
    <properties xmi:id="Property_1183122130081" name="com.ibm.ws.orb.transport.WSSSLClientSocketFactoryName" value="com.ibm.ws.security.orbssl.WSSSLClientSocketFactoryImpl"/>
    <properties xmi:id="Property_1183122130082" name="com.ibm.CORBA.ConnectionInterceptorName" value="com.ibm.ISecurityLocalObjectBaseL13Impl.SecurityConnectionInterceptor"/>
    <properties xmi:id="Property_1183122130083" name="com.ibm.CORBA.RasManager" value="com.ibm.websphere.ras.WsOrbRasManager"/>
    <properties xmi:id="Property_1183122130084" name="com.ibm.ws.orb.transport.useMultiHome" value="true"/>
    <properties xmi:id="Property_1183122130085" name="com.ibm.websphere.management.registerServerIORWithLSD" value="true"/>
    <interceptors xmi:id="Interceptor_1183122130078" name="com.ibm.ejs.ras.RasContextSupport"/>
    <interceptors xmi:id="Interceptor_1183122130079" name="com.ibm.ws.runtime.workloadcontroller.OrbWorkloadRequestInterceptor"/>
    <interceptors xmi:id="Interceptor_1183122130080" name="com.ibm.ws.Transaction.JTS.TxInterceptorInitializer"/>
    <interceptors xmi:id="Interceptor_1183122130081" name="com.ibm.ISecurityLocalObjectBaseL13Impl.SecurityComponentFactory"/>
    <interceptors xmi:id="Interceptor_1183122130082" name="com.ibm.ISecurityLocalObjectBaseL13Impl.ServerRIWrapper"/>
    <interceptors xmi:id="Interceptor_1183122130083" name="com.ibm.ISecurityLocalObjectBaseL13Impl.ClientRIWrapper"/>
    <interceptors xmi:id="Interceptor_1183122130084" name="com.ibm.ISecurityLocalObjectBaseL13Impl.CSIClientRI"/>
    <interceptors xmi:id="Interceptor_1183122130085" name="com.ibm.ISecurityLocalObjectBaseL13Impl.CSIServerRI"/>
    <interceptors xmi:id="Interceptor_1183122130086" name="com.ibm.ws.wlm.client.WLMClientInitializer"/>
    <interceptors xmi:id="Interceptor_1183122130087" name="com.ibm.ws.wlm.server.WLMServerInitializer"/>
    <interceptors xmi:id="Interceptor_1183122130088" name="com.ibm.ws.activity.remote.cos.ActivityServiceServerInterceptor"/>
    <interceptors xmi:id="Interceptor_1183122130089" name="com.ibm.debug.DebugPortableInterceptor"/>
    <interceptors xmi:id="Interceptor_1183122130090" name="com.ibm.debug.olt.ivbtrjrt.OLT_RI"/>
    <plugins xmi:id="ORBPlugin_1183122130078" name="com.ibm.ws.orbimpl.transport.WSTransport"/>
    <plugins xmi:id="ORBPlugin_1183122130079" name="com.ibm.ISecurityUtilityImpl.SecurityPropertyManager"/>
    <plugins xmi:id="ORBPlugin_1183122130080" name="com.ibm.ws.orbimpl.WSORBPropertyManager"/>
    <plugins xmi:id="ORBPlugin_1183122130081" name="com.ibm.ws.wlm.client.WLMClient"/>
    <plugins xmi:id="ORBPlugin_1183122130082" name="com.ibm.ws.pmi.server.modules.OrbPerfModule"/>
    <plugins xmi:id="ORBPlugin_1183122130083" name="com.ibm.ws.csi.CORBAORBMethodAccessControl"/>
    <threadPool xmi:id="ThreadPool_1183122130078" minimumSize="10" maximumSize="50" inactivityTimeout="3500" isGrowable="false" name="ORB.thread.pool"/>
  </services>
  <services xmi:type="channelservice:TransportChannelService" xmi:id="TransportChannelService_1183122130078" enable="true">
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130078" name="TCP_1" endPointName="WC_adminhost" maxOpenConnections="20000" inactivityTimeout="60" threadPool="ThreadPool_1183122130079"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130079" name="TCP_2" endPointName="WC_defaulthost" maxOpenConnections="20000" inactivityTimeout="60" threadPool="ThreadPool_1183122130079"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130080" name="TCP_3" endPointName="WC_adminhost_secure" maxOpenConnections="20000" inactivityTimeout="60" threadPool="ThreadPool_1183122130079"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130081" name="TCP_4" endPointName="WC_defaulthost_secure" maxOpenConnections="20000" inactivityTimeout="60" threadPool="ThreadPool_1183122130079"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130082" name="TCP_5" endPointName="DCS_UNICAST_ADDRESS" maxOpenConnections="20000" inactivityTimeout="60" threadPool="ThreadPool_1183122130080"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130083" name="TCP_8" discriminationWeight="0" endPointName="SIP_DEFAULTHOST" maxOpenConnections="20000" inactivityTimeout="60"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130084" name="TCP_9" discriminationWeight="0" endPointName="SIP_DEFAULTHOST_SECURE" maxOpenConnections="20000" inactivityTimeout="60"/>
    <transportChannels xmi:type="channelservice.channels:SSLInboundChannel" xmi:id="SSLInboundChannel_1183122130078" name="SSL_1" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:SSLInboundChannel" xmi:id="SSLInboundChannel_1183122130079" name="SSL_2" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:SSLInboundChannel" xmi:id="SSLInboundChannel_1183122130080" name="SSL_3" discriminationWeight="2"/>
    <transportChannels xmi:type="channelservice.channels:SSLInboundChannel" xmi:id="SSLInboundChannel_1183122130081" name="SSL_5" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:HTTPInboundChannel" xmi:id="HTTPInboundChannel_1183122130078" name="HTTP_1" discriminationWeight="10" maximumPersistentRequests="100" keepAlive="true" readTimeout="60" writeTimeout="60" persistentTimeout="30" enableLogging="false"/>
    <transportChannels xmi:type="channelservice.channels:HTTPInboundChannel" xmi:id="HTTPInboundChannel_1183122130079" name="HTTP_2" discriminationWeight="1" maximumPersistentRequests="100" keepAlive="true" readTimeout="60" writeTimeout="60" persistentTimeout="30" enableLogging="false"/>
    <transportChannels xmi:type="channelservice.channels:HTTPInboundChannel" xmi:id="HTTPInboundChannel_1183122130080" name="HTTP_3" discriminationWeight="10" maximumPersistentRequests="100" keepAlive="true" readTimeout="60" writeTimeout="60" persistentTimeout="30" enableLogging="false"/>
    <transportChannels xmi:type="channelservice.channels:HTTPInboundChannel" xmi:id="HTTPInboundChannel_1183122130081" name="HTTP_4" discriminationWeight="10" maximumPersistentRequests="100" keepAlive="true" readTimeout="60" writeTimeout="60" persistentTimeout="30" enableLogging="false"/>
    <transportChannels xmi:type="channelservice.channels:HTTPQueueInboundChannel" xmi:id="HTTPQueueInboundChannel_1183122130078" name="HTTPQ_1" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:HTTPQueueInboundChannel" xmi:id="HTTPQueueInboundChannel_1183122130079" name="HTTPQ_2" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:WebContainerInboundChannel" xmi:id="WebContainerInboundChannel_1183122130078" name="WCC_1" discriminationWeight="10" writeBufferSize="32768"/>
    <transportChannels xmi:type="channelservice.channels:WebContainerInboundChannel" xmi:id="WebContainerInboundChannel_1183122130079" name="WCC_2" discriminationWeight="10" writeBufferSize="32768"/>
    <transportChannels xmi:type="channelservice.channels:WebContainerInboundChannel" xmi:id="WebContainerInboundChannel_1183122130080" name="WCC_3" discriminationWeight="10" writeBufferSize="32768"/>
    <transportChannels xmi:type="channelservice.channels:WebContainerInboundChannel" xmi:id="WebContainerInboundChannel_1183122130081" name="WCC_4" discriminationWeight="10" writeBufferSize="32768"/>
    <transportChannels xmi:type="channelservice.channels:WebContainerInboundChannel" xmi:id="WebContainerInboundChannel_1183122130082" name="WCC_5" discriminationWeight="10" writeBufferSize="32768"/>
    <transportChannels xmi:type="channelservice.channels:WebContainerInboundChannel" xmi:id="WebContainerInboundChannel_1183122130083" name="WCC_6" discriminationWeight="10" writeBufferSize="32768"/>
    <transportChannels xmi:type="channelservice.channels:DCSInboundChannel" xmi:id="DCSInboundChannel_1183122130078" name="DCS_1" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:DCSInboundChannel" xmi:id="DCSInboundChannel_1183122130079" name="DCS_2" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:SIPInboundChannel" xmi:id="SIPInboundChannel_1183122130078" name="SIP_1" discriminationWeight="10"/>
    <transportChannels xmi:type="channelservice.channels:SIPInboundChannel" xmi:id="SIPInboundChannel_1183122130079" name="SIP_2" discriminationWeight="10"/>
    <transportChannels xmi:type="channelservice.channels:SIPInboundChannel" xmi:id="SIPInboundChannel_1183122130080" name="SIP_3" discriminationWeight="10"/>
    <transportChannels xmi:type="channelservice.channels:SIPContainerInboundChannel" xmi:id="SIPContainerInboundChannel_1183122130078" name="SIPCC_1" discriminationWeight="100"/>
    <transportChannels xmi:type="channelservice.channels:SIPContainerInboundChannel" xmi:id="SIPContainerInboundChannel_1183122130079" name="SIPCC_2" discriminationWeight="100"/>
    <transportChannels xmi:type="channelservice.channels:SIPContainerInboundChannel" xmi:id="SIPContainerInboundChannel_1183122130080" name="SIPCC_3" discriminationWeight="100"/>
    <transportChannels xmi:type="channelservice.channels:UDPInboundChannel" xmi:id="UDPInboundChannel_1183122130078" name="UDP_1" discriminationWeight="0" endPointName="SIP_DEFAULTHOST"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130085" name="SIB_TCP_JFAP" endPointName="SIB_ENDPOINT_ADDRESS" threadPool="ThreadPool_1183122130081"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130086" name="SIB_TCP_JFAP_SSL" endPointName="SIB_ENDPOINT_SECURE_ADDRESS" threadPool="ThreadPool_1183122130081"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130087" name="SIB_TCP_MQFAP" endPointName="SIB_MQ_ENDPOINT_ADDRESS" threadPool="ThreadPool_1183122130081"/>
    <transportChannels xmi:type="channelservice.channels:TCPInboundChannel" xmi:id="TCPInboundChannel_1183122130088" name="SIB_TCP_MQFAP_SSL" endPointName="SIB_MQ_ENDPOINT_SECURE_ADDRESS" threadPool="ThreadPool_1183122130081"/>
    <transportChannels xmi:type="channelservice.channels:SSLInboundChannel" xmi:id="SSLInboundChannel_1183122130082" name="SIB_SSL_JFAP" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:SSLInboundChannel" xmi:id="SSLInboundChannel_1183122130083" name="SIB_SSL_MQFAP" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:JFAPInboundChannel" xmi:id="JFAPInboundChannel_1183122130078" name="SIB_JFAP" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:JFAPInboundChannel" xmi:id="JFAPInboundChannel_1183122130079" name="SIB_JFAP_SSL" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:MQFAPInboundChannel" xmi:id="MQFAPInboundChannel_1183122130078" name="SIB_MQFAP" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:MQFAPInboundChannel" xmi:id="MQFAPInboundChannel_1183122130079" name="SIB_MQFAP_SSL" discriminationWeight="1"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130078" name="SIB_TCP_JFAP_OUT" inactivityTimeout="60" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130079" name="SIB_TCP_JFAP_SSL_OUT" inactivityTimeout="60" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130080" name="SIB_TCP_JFAP_TUN_OUT" inactivityTimeout="60" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130081" name="SIB_TCP_JFAP_TUN_SSL_OUT" inactivityTimeout="60" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130082" name="SIB_TCP_MQFAP_OUT" inactivityTimeout="60" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130083" name="SIB_TCP_MQFAP_SSL_OUT" inactivityTimeout="60" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:SSLOutboundChannel" xmi:id="SSLOutboundChannel_1183122130078" name="SIB_SSL_MQFAP_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:SSLOutboundChannel" xmi:id="SSLOutboundChannel_1183122130079" name="SIB_SSL_JFAP_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:SSLOutboundChannel" xmi:id="SSLOutboundChannel_1183122130080" name="SIB_SSL_JFAP_TUN_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:HTTPOutboundChannel" xmi:id="HTTPOutboundChannel_1183122130078" name="SIB_HTTP_JFAP_TUN_OUT"/>
    <transportChannels xmi:type="channelservice.channels:HTTPOutboundChannel" xmi:id="HTTPOutboundChannel_1183122130079" name="SIB_HTTP_JFAP_TUN_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:HTTPTunnelOutboundChannel" xmi:id="HTTPTunnelOutboundChannel_1183122130078" name="SIB_HTC_JFAP_TUN_OUT"/>
    <transportChannels xmi:type="channelservice.channels:HTTPTunnelOutboundChannel" xmi:id="HTTPTunnelOutboundChannel_1183122130079" name="SIB_HTC_JFAP_TUN_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:JFAPOutboundChannel" xmi:id="JFAPOutboundChannel_1183122130078" name="SIB_JFAP_JFAP_OUT"/>
    <transportChannels xmi:type="channelservice.channels:JFAPOutboundChannel" xmi:id="JFAPOutboundChannel_1183122130079" name="SIB_JFAP_JFAP_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:JFAPOutboundChannel" xmi:id="JFAPOutboundChannel_1183122130080" name="SIB_JFAP_JFAP_TUN_OUT"/>
    <transportChannels xmi:type="channelservice.channels:JFAPOutboundChannel" xmi:id="JFAPOutboundChannel_1183122130081" name="SIB_JFAP_JFAP_TUN_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:MQFAPOutboundChannel" xmi:id="MQFAPOutboundChannel_1183122130078" name="SIB_MQFAP_MQFAP_SSL_OUT"/>
    <transportChannels xmi:type="channelservice.channels:MQFAPOutboundChannel" xmi:id="MQFAPOutboundChannel_1183122130079" name="SIB_MQFAP_MQFAP_OUT"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130084" name="SIB_TCP_RMQ_OUT" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:TCPOutboundChannel" xmi:id="TCPOutboundChannel_1183122130085" name="SIB_TCP_RMQ_SSL_OUT" threadPool="ThreadPool_1183122130082"/>
    <transportChannels xmi:type="channelservice.channels:SSLOutboundChannel" xmi:id="SSLOutboundChannel_1183122130081" name="SIB_SSL_RMQ_SSL_OUT"/>
    <transportChannels xmi:type="sibresources:RMQOutboundChannel" xmi:id="RMQOutboundChannel_1183122130078" name="SIB_RMQ_RMQ_SSL_OUT"/>
    <transportChannels xmi:type="sibresources:RMQOutboundChannel" xmi:id="RMQOutboundChannel_1183122130079" name="SIB_RMQ_RMQ_OUT"/>
    <chains xmi:id="Chain_1183122130078" name="WCInboundAdmin" enable="true" transportChannels="TCPInboundChannel_1183122130078 HTTPInboundChannel_1183122130078 WebContainerInboundChannel_1183122130078"/>
    <chains xmi:id="Chain_1183122130079" name="WCInboundDefault" enable="true" transportChannels="TCPInboundChannel_1183122130079 HTTPInboundChannel_1183122130079 WebContainerInboundChannel_1183122130079"/>
    <chains xmi:id="Chain_1183122130080" name="HttpQueueInboundDefault" enable="true" transportChannels="TCPInboundChannel_1183122130079 HTTPInboundChannel_1183122130079 HTTPQueueInboundChannel_1183122130078 WebContainerInboundChannel_1183122130082"/>
    <chains xmi:id="Chain_1183122130081" name="HttpQueueInboundDefaultSecure" enable="true" transportChannels="TCPInboundChannel_1183122130081 SSLInboundChannel_1183122130079 HTTPInboundChannel_1183122130081 HTTPQueueInboundChannel_1183122130079 WebContainerInboundChannel_1183122130083"/>
    <chains xmi:id="Chain_1183122130082" name="WCInboundAdminSecure" enable="true" transportChannels="TCPInboundChannel_1183122130080 SSLInboundChannel_1183122130078 HTTPInboundChannel_1183122130080 WebContainerInboundChannel_1183122130080"/>
    <chains xmi:id="Chain_1183122130083" name="WCInboundDefaultSecure" enable="true" transportChannels="TCPInboundChannel_1183122130081 SSLInboundChannel_1183122130079 HTTPInboundChannel_1183122130081 WebContainerInboundChannel_1183122130081"/>
    <chains xmi:id="Chain_1183122130084" name="DCS" enable="true" transportChannels="TCPInboundChannel_1183122130082 DCSInboundChannel_1183122130078"/>
    <chains xmi:id="Chain_1183122130085" name="DCS-Secure" enable="true" transportChannels="TCPInboundChannel_1183122130082 SSLInboundChannel_1183122130080 DCSInboundChannel_1183122130079"/>
    <chains xmi:id="Chain_1183122130086" name="SIPCInboundDefault" enable="true" transportChannels="TCPInboundChannel_1183122130083 SIPInboundChannel_1183122130078 SIPContainerInboundChannel_1183122130078"/>
    <chains xmi:id="Chain_1183122130087" name="SIPCInboundDefaultSecure" enable="true" transportChannels="TCPInboundChannel_1183122130084 SSLInboundChannel_1183122130081 SIPInboundChannel_1183122130079 SIPContainerInboundChannel_1183122130079"/>
    <chains xmi:id="Chain_1183122130088" name="SIPCInboundDefaultUDP" enable="true" transportChannels="UDPInboundChannel_1183122130078 SIPInboundChannel_1183122130080 SIPContainerInboundChannel_1183122130080"/>
    <chains xmi:id="Chain_1183122130089" name="InboundBasicMessaging" enable="true" transportChannels="TCPInboundChannel_1183122130085 JFAPInboundChannel_1183122130078"/>
    <chains xmi:id="Chain_1183122130090" name="InboundSecureMessaging" enable="true" transportChannels="TCPInboundChannel_1183122130086 SSLInboundChannel_1183122130082 JFAPInboundChannel_1183122130079"/>
    <chains xmi:id="Chain_1183122130091" name="InboundBasicMQLink" enable="true" transportChannels="TCPInboundChannel_1183122130087 MQFAPInboundChannel_1183122130078"/>
    <chains xmi:id="Chain_1183122130092" name="InboundSecureMQLink" enable="true" transportChannels="TCPInboundChannel_1183122130088 SSLInboundChannel_1183122130083 MQFAPInboundChannel_1183122130079"/>
    <chains xmi:id="Chain_1183122130093" name="BootstrapBasicMessaging" transportChannels="JFAPOutboundChannel_1183122130078 TCPOutboundChannel_1183122130078"/>
    <chains xmi:id="Chain_1183122130094" name="BootstrapSecureMessaging" transportChannels="JFAPOutboundChannel_1183122130079 SSLOutboundChannel_1183122130079 TCPOutboundChannel_1183122130079"/>
    <chains xmi:id="Chain_1183122130095" name="BootstrapTunneledMessaging" transportChannels="JFAPOutboundChannel_1183122130080 HTTPTunnelOutboundChannel_1183122130078 HTTPOutboundChannel_1183122130078 TCPOutboundChannel_1183122130080"/>
    <chains xmi:id="Chain_1183122130096" name="BootstrapTunneledSecureMessaging" transportChannels="JFAPOutboundChannel_1183122130081 HTTPTunnelOutboundChannel_1183122130079 HTTPOutboundChannel_1183122130079 SSLOutboundChannel_1183122130080 TCPOutboundChannel_1183122130081"/>
    <chains xmi:id="Chain_1183122130097" name="OutboundBasicMQLink" transportChannels="MQFAPOutboundChannel_1183122130079 TCPOutboundChannel_1183122130082"/>
    <chains xmi:id="Chain_1183122130098" name="OutboundSecureMQLink" transportChannels="MQFAPOutboundChannel_1183122130078 SSLOutboundChannel_1183122130078 TCPOutboundChannel_1183122130083"/>
    <chains xmi:id="Chain_1183122130099" name="OutboundBasicWMQClient" transportChannels="RMQOutboundChannel_1183122130079 TCPOutboundChannel_1183122130084"/>
    <chains xmi:id="Chain_1183122130100" name="OutboundSecureWMQClient" transportChannels="RMQOutboundChannel_1183122130078 SSLOutboundChannel_1183122130081 TCPOutboundChannel_1183122130085"/>
  </services>
  <services xmi:type="threadpoolmanager:ThreadPoolManager" xmi:id="ThreadPoolManager_1183122130078" enable="true">
    <threadPools xmi:id="ThreadPool_1183122130083" minimumSize="10" maximumSize="50" inactivityTimeout="3500" isGrowable="false" name="ORB.thread.pool"/>
    <threadPools xmi:id="ThreadPool_1183122130084" minimumSize="1" maximumSize="3" inactivityTimeout="30000" isGrowable="false" name="server.startup" description="This pool is used by WebSphere during server startup."/>
    <threadPools xmi:id="ThreadPool_1183122130085" minimumSize="20" maximumSize="20" name="Default"/>
    <threadPools xmi:id="ThreadPool_1183122130079" minimumSize="50" maximumSize="50" inactivityTimeout="60000" isGrowable="false" name="WebContainer"/>
    <threadPools xmi:id="ThreadPool_1183122130080" minimumSize="20" maximumSize="20" isGrowable="false" name="TCPChannel.DCS"/>
    <threadPools xmi:id="ThreadPool_1183122130081" minimumSize="4" maximumSize="50" name="SIBFAPInboundThreadPool" description="Service integration bus FAP inbound channel thread pool"/>
    <threadPools xmi:id="ThreadPool_1183122130082" minimumSize="4" maximumSize="50" name="SIBFAPThreadPool" description="Service integration bus FAP outbound channel thread pool"/>
    <threadPools xmi:id="ThreadPool_1747817537050" minimumSize="35" maximumSize="41" inactivityTimeout="3500" name="SIBJMSRAThreadPool" description="Service Integration Bus JMS Resource Adapter thread pool"/>
    <threadPools xmi:id="ThreadPool_1747817537153" minimumSize="10" maximumSize="50" name="WMQJCAResourceAdapter" description="WebSphere MQ Resource Adapter thread pool"/>
  </services>
  <services xmi:type="loggingservice.http:HTTPAccessLoggingService" xmi:id="HTTPAccessLoggingService_1183122130078" enable="false" enableErrorLogging="true" enableAccessLogging="true">
    <errorLog xmi:id="LogFile_1183122130078" filePath="${SERVER_LOG_ROOT}/http_error.log" maximumSize="500"/>
    <accessLog xmi:id="LogFile_1183122130079" filePath="${SERVER_LOG_ROOT}/http_access.log" maximumSize="500"/>
  </services>
  <errorStreamRedirect xmi:id="StreamRedirect_1183122130078" fileName="${SERVER_LOG_ROOT}/SystemErr.log" rolloverType="SIZE" maxNumberOfBackupFiles="5" rolloverSize="1" baseHour="24" rolloverPeriod="24" formatWrites="true" messageFormatKind="BASIC" suppressWrites="false" suppressStackTrace="false"/>
  <outputStreamRedirect xmi:id="StreamRedirect_1183122130079" fileName="${SERVER_LOG_ROOT}/SystemOut.log" rolloverType="SIZE" maxNumberOfBackupFiles="5" rolloverSize="1" baseHour="24" rolloverPeriod="24" formatWrites="true" messageFormatKind="BASIC" suppressWrites="false" suppressStackTrace="false"/>
  <components xmi:type="namingserver:NameServer" xmi:id="NameServer_1183122130078">
    <stateManagement xmi:id="StateManageable_1183122130079" initialState="START"/>
  </components>
  <components xmi:type="applicationserver:ApplicationServer" xmi:id="ApplicationServer_1183122130078" applicationClassLoaderPolicy="MULTIPLE">
    <stateManagement xmi:id="StateManageable_1183122130080" initialState="START"/>
    <services xmi:type="applicationserver:TransactionService" xmi:id="TransactionService_1183122130078" enable="true" totalTranLifetimeTimeout="120" clientInactivityTimeout="60" propogatedOrBMTTranLifetimeTimeout="300" httpProxyPrefix="" httpsProxyPrefix=""/>
    <services xmi:type="applicationserver:DynamicCache" xmi:id="DynamicCache_1183122130078" enable="true">
      <cacheGroups xmi:id="ExternalCacheGroup_1183122130078" name="EsiInvalidator">
        <members xmi:id="ExternalCacheGroupMember_1183122130078" address="localhost" adapterBeanName="com.ibm.websphere.servlet.cache.ESIInvalidatorServlet"/>
      </cacheGroups>
    </services>
    <components xmi:type="applicationserver.webcontainer:WebContainer" xmi:id="WebContainer_1183122130078" enableServletCaching="false" disablePooling="false" asyncRunnableWorkManager="wm/default">
      <stateManagement xmi:id="StateManageable_1183122130081" initialState="START"/>
      <services xmi:type="applicationserver.webcontainer:SessionManager" xmi:id="SessionManager_1183122130078" enable="true" enableUrlRewriting="false" enableCookies="true" enableSSLTracking="false" enableProtocolSwitchRewriting="false" sessionPersistenceMode="NONE" enableSecurityIntegration="true" allowSerializedSessionAccess="false" maxWaitTime="5" accessSessionOnTimeout="true">
        <defaultCookieSettings xmi:id="Cookie_1183122130078" domain="" maximumAge="-1" secure="false"/>
        <sessionDatabasePersistence xmi:id="SessionDatabasePersistence_1183122130078" datasourceJNDIName="jdbc/Sessions" userId="db2admin" password="{xor}Oz1tPjsyNjE=" db2RowSize="ROW_SIZE_4KB" tableSpaceName=""/>
        <tuningParams xmi:id="TuningParams_1183122130078" usingMultiRowSchema="false" maxInMemorySessionCount="1000" allowOverflow="true" scheduleInvalidation="false" writeFrequency="TIME_BASED_WRITE" writeInterval="10" writeContents="ONLY_UPDATED_ATTRIBUTES" invalidationTimeout="30">
          <invalidationSchedule xmi:id="InvalidationSchedule_1183122130078" firstHour="14" secondHour="2"/>
        </tuningParams>
      </services>
      <properties xmi:id="Property_1749890096759" name="trusthostheaderport" value="true"/>
      <properties xmi:id="Property_1749890096775" name="com.ibm.ws.webcontainer.extractHostHeaderPort" value="true"/>
    </components>
    <components xmi:type="applicationserver.ejbcontainer:EJBContainer" xmi:id="EJBContainer_1183122130078" passivationDirectory="${USER_INSTALL_ROOT}/temp" inactivePoolCleanupInterval="30000">
      <stateManagement xmi:id="StateManageable_1183122130082" initialState="START"/>
      <services xmi:type="applicationserver.ejbcontainer.messagelistener:MessageListenerService" xmi:id="MessageListenerService_1183122130078">
        <threadPool xmi:id="ThreadPool_1183122130086" minimumSize="10" maximumSize="50" inactivityTimeout="3500" isGrowable="false" name="Message.Listener.Pool"/>
      </services>
      <cacheSettings xmi:id="EJBCache_1183122130078" cleanupInterval="3000" cacheSize="2053"/>
      <timerSettings xmi:id="EJBTimer_1183122130078" tablePrefix="EJBTIMER_" pollInterval="300" numAlarmThreads="1" numNPTimerThreads="1" nonPersistentTimerRetryCount="-1" nonPersistentTimerRetryInterval="300" uniqueTimerManagerForNP="false" datasourceJNDIName="jdbc/DefaultEJBTimerDataSource"/>
      <asyncSettings xmi:id="EJBAsync_1183122130078" maxThreads="5" workReqQSize="0" workReqQFullAction="0" customWorkManagerJNDIName="" futureTimeout="86400" useCustomDefinedWM="false"/>
    </components>
    <components xmi:type="portletcontainer:PortletContainer" xmi:id="PortletContainer_1183122130078"/>
    <components xmi:type="applicationserver.sipcontainer:SIPContainer" xmi:id="SIPContainer_1183122130078" name="" maxAppSessions="120000" maxMessageRate="5000" maxDispatchQueueSize="3200" maxResponseTime="0" statAveragePeriod="1000" statUpdateRange="10000">
      <stack xmi:id="Stack_1183122130078">
        <timers xmi:id="Timers_1183122130078"/>
      </stack>
    </components>
    <webserverPluginSettings xmi:id="WebserverPluginSettings_1183122130078" WaitForContinue="false" ConnectTimeout="5" MaxConnections="-1" ExtendedHandshake="false" ServerIOTimeout="900"/>
  </components>
  <processDefinitions xmi:type="processexec:JavaProcessDef" xmi:id="JavaProcessDef_1183122130078" workingDirectory="${USER_INSTALL_ROOT}" executableTargetKind="JAVA_CLASS" executableTarget="com.ibm.ws.runtime.WsServer">
    <execution xmi:id="ProcessExecution_1183122130078" processPriority="20" runAsUser="" runAsGroup=""/>
    <ioRedirect xmi:id="OutputRedirect_1183122130078" stdoutFilename="${SERVER_LOG_ROOT}/native_stdout.log" stderrFilename="${SERVER_LOG_ROOT}/native_stderr.log"/>
    <monitoringPolicy xmi:id="MonitoringPolicy_1183122130078" maximumStartupAttempts="3" pingInterval="60" pingTimeout="300" autoRestart="true" nodeRestartState="STOPPED"/>
    <jvmEntries xmi:id="JavaVirtualMachine_1183122130078" verboseModeClass="false" verboseModeJNI="false" runHProf="false" hprofArguments="" debugMode="false" debugArgs="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=7777" genericJvmArguments="-Xnoloa">
      <systemProperties xmi:id="Property_1" name="com.ibm.security.jgss.debug" value="off" required="false"/>
      <systemProperties xmi:id="Property_2" name="com.ibm.security.krb5.Krb5Debug" value="off" required="false"/>
      <systemProperties xmi:id="Property_1747817572229" name="java.util.prefs.userRoot" value="/home/<USER>/"/>
    </jvmEntries>
  </processDefinitions>
</process:Server>
