#!/bin/bash

# ActiveMQ Queue Initialization Script
# This script creates the necessary queues and topics for the Todo application

echo "=== Initializing ActiveMQ Queues for Todo Application ==="

# Wait for ActiveMQ to be ready
echo "Waiting for ActiveMQ to start..."
sleep 30

# ActiveMQ Admin URL
ACTIVEMQ_URL="http://activemq:8161"
ADMIN_USER="admin"
ADMIN_PASS="admin123"

# Function to create a queue
create_queue() {
    local queue_name=$1
    echo "Creating queue: $queue_name"
    
    curl -u "$ADMIN_USER:$ADMIN_PASS" \
         -X POST \
         "$ACTIVEMQ_URL/admin/queues.jsp" \
         -d "JMSDestination=$queue_name&JMSDestinationType=queue&secret=&action=create" \
         -H "Content-Type: application/x-www-form-urlencoded"
    
    if [ $? -eq 0 ]; then
        echo "✅ Queue $queue_name created successfully"
    else
        echo "❌ Failed to create queue $queue_name"
    fi
}

# Function to create a topic
create_topic() {
    local topic_name=$1
    echo "Creating topic: $topic_name"
    
    curl -u "$ADMIN_USER:$ADMIN_PASS" \
         -X POST \
         "$ACTIVEMQ_URL/admin/topics.jsp" \
         -d "JMSDestination=$topic_name&JMSDestinationType=topic&secret=&action=create" \
         -H "Content-Type: application/x-www-form-urlencoded"
    
    if [ $? -eq 0 ]; then
        echo "✅ Topic $topic_name created successfully"
    else
        echo "❌ Failed to create topic $topic_name"
    fi
}

# Create Todo operation queues
echo "Creating Todo operation queues..."
create_queue "todo.create.queue"
create_queue "todo.update.queue"
create_queue "todo.delete.queue"
create_queue "todo.toggle.queue"

# Create Dead Letter Queues
echo "Creating Dead Letter Queues..."
create_queue "DLQ.todo.create.queue"
create_queue "DLQ.todo.update.queue"
create_queue "DLQ.todo.delete.queue"
create_queue "DLQ.todo.toggle.queue"

# Create notification topics
echo "Creating notification topics..."
create_topic "todo.notifications"
create_topic "todo.statistics"

# Create audit queue
echo "Creating audit queue..."
create_queue "todo.audit.queue"

echo "=== ActiveMQ Queue Initialization Complete ==="

# Display queue status
echo "=== Queue Status ==="
curl -u "$ADMIN_USER:$ADMIN_PASS" \
     "$ACTIVEMQ_URL/admin/xml/queues.jsp" \
     2>/dev/null | grep -o 'name="[^"]*"' | sed 's/name="//g' | sed 's/"//g'

echo "=== Topic Status ==="
curl -u "$ADMIN_USER:$ADMIN_PASS" \
     "$ACTIVEMQ_URL/admin/xml/topics.jsp" \
     2>/dev/null | grep -o 'name="[^"]*"' | sed 's/name="//g' | sed 's/"//g'

echo "ActiveMQ Web Console: $ACTIVEMQ_URL"
echo "Username: $ADMIN_USER"
echo "Password: $ADMIN_PASS"
