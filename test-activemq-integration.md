# 🧪 ActiveMQ Integration Testing Guide

## 🎯 Test Objectives

This comprehensive test plan validates the ActiveMQ integration with the Todo application, focusing on:
- ✅ **Asynchronous Message Processing**
- ✅ **Database Resilience Features**
- ✅ **Circuit Breaker Functionality**
- ✅ **Message Queue Operations**
- ✅ **Dead Letter Queue Handling**
- ✅ **System Recovery Scenarios**

## 📋 Prerequisites

### **Infrastructure Status**
- ✅ **ActiveMQ Broker**: Running on port 61616 (JMS) and 8161 (Web Console)
- ✅ **Oracle Database**: Running with todo tables created
- ✅ **WebSphere Server**: Running with ActiveMQ client libraries installed
- ✅ **Todo Application**: Deployed with messaging and resilience features

### **Access Points**
- **ActiveMQ Console**: http://localhost:8161 (admin/admin123)
- **WebSphere Console**: https://localhost:9043/ibm/console
- **Todo Application**: http://localhost:9080/todo-list-app/
- **Resilience Dashboard**: http://localhost:9080/todo-list-app/resilience/dashboard

## 🔧 Test Environment Setup

### **1. Verify ActiveMQ Status**
```bash
# Check ActiveMQ container
docker ps | grep activemq

# Check ActiveMQ logs
docker logs activemq --tail 20

# Test ActiveMQ web console
curl -u admin:admin123 http://localhost:8161/admin/
```

### **2. Verify WebSphere Status**
```bash
# Check WebSphere container
docker ps | grep appserver

# Check WebSphere logs
docker logs appserver --tail 20

# Test WebSphere admin console
curl -k https://localhost:9043/ibm/console/
```

### **3. Verify Database Connectivity**
```bash
# Test Oracle database
docker exec oracle sqlplus -S system/SavvySpend123@XEPDB1 <<< "SELECT COUNT(*) FROM todos;"
```

## 🧪 Test Scenarios

### **Test 1: Basic Message Queue Functionality**

**Objective**: Verify ActiveMQ queues are working correctly

**Steps**:
1. Open ActiveMQ Web Console: http://localhost:8161
2. Login with admin/admin123
3. Navigate to "Queues" tab
4. Verify the following queues exist:
   - `todo.create.queue`
   - `todo.update.queue`
   - `todo.delete.queue`
   - `todo.toggle.queue`
   - `todo.audit.queue`

**Expected Results**:
- ✅ All queues are visible in the console
- ✅ Queue statistics show 0 pending messages initially
- ✅ No errors in ActiveMQ logs

### **Test 2: Todo Application Basic Operations**

**Objective**: Test basic CRUD operations work without messaging

**Steps**:
1. Open Todo Application: http://localhost:9080/todo-list-app/
2. Create a new todo item
3. Update the todo item
4. Toggle completion status
5. Delete the todo item

**Expected Results**:
- ✅ All operations complete successfully
- ✅ Database is updated correctly
- ✅ UI reflects changes immediately

### **Test 3: Asynchronous Message Processing**

**Objective**: Verify messages are sent to ActiveMQ queues

**Steps**:
1. Open ActiveMQ Console in one browser tab
2. Open Todo Application in another tab
3. Perform todo operations while monitoring queue statistics
4. Check queue message counts after each operation

**Expected Results**:
- ✅ Messages appear in appropriate queues after operations
- ✅ Message counts increase with each operation
- ✅ Messages are processed and removed from queues

### **Test 4: Database Resilience - Circuit Breaker**

**Objective**: Test circuit breaker functionality when database fails

**Steps**:
1. Open Resilience Dashboard: http://localhost:9080/todo-list-app/resilience/dashboard
2. Verify initial status shows "HEALTHY"
3. Stop Oracle database: `docker stop oracle`
4. Attempt todo operations in the application
5. Monitor circuit breaker state changes
6. Restart database: `docker start oracle`
7. Monitor recovery

**Expected Results**:
- ✅ Circuit breaker opens after consecutive failures
- ✅ Operations are queued for async processing
- ✅ Circuit breaker recovers when database is restored
- ✅ Queued operations are processed after recovery

### **Test 5: Retry Mechanism Testing**

**Objective**: Verify retry logic works correctly

**Steps**:
1. Monitor resilience dashboard
2. Simulate intermittent database issues
3. Observe retry attempts and backoff behavior
4. Check application logs for retry messages

**Expected Results**:
- ✅ Operations retry with exponential backoff
- ✅ Successful operations reset failure counters
- ✅ Max retry attempts are respected

### **Test 6: Dead Letter Queue Handling**

**Objective**: Test message handling when processing fails repeatedly

**Steps**:
1. Create conditions that cause message processing to fail
2. Monitor DLQ queues in ActiveMQ console
3. Verify failed messages are moved to DLQ
4. Check DLQ message content

**Expected Results**:
- ✅ Failed messages are moved to appropriate DLQ
- ✅ DLQ messages contain original operation data
- ✅ System continues processing other messages

### **Test 7: Performance Under Load**

**Objective**: Test system behavior under high message volume

**Steps**:
1. Create multiple todo operations rapidly
2. Monitor queue depths and processing rates
3. Check for message backlog or processing delays
4. Verify system stability

**Expected Results**:
- ✅ System handles high message volume
- ✅ No message loss occurs
- ✅ Processing remains stable
- ✅ Memory usage stays within limits

### **Test 8: System Recovery Scenarios**

**Objective**: Test complete system recovery after failures

**Steps**:
1. Stop all services: `docker-compose down`
2. Start services: `docker-compose up -d`
3. Verify all components recover correctly
4. Test end-to-end functionality

**Expected Results**:
- ✅ All services start successfully
- ✅ Persistent queues retain messages
- ✅ Database connections are restored
- ✅ Application functionality is fully restored

## 📊 Monitoring and Validation

### **ActiveMQ Metrics to Monitor**
- Queue depths (pending messages)
- Message throughput (enqueue/dequeue rates)
- Consumer connection status
- Memory usage
- Dead letter queue activity

### **Application Metrics to Monitor**
- Circuit breaker state
- Database connection health
- Retry attempt counts
- Operation success/failure rates
- Response times

### **Database Metrics to Monitor**
- Connection pool status
- Transaction success rates
- Query execution times
- Lock contention

## 🚨 Troubleshooting Guide

### **Common Issues and Solutions**

**Issue**: Messages not appearing in queues
- **Check**: JMS connection factory configuration
- **Solution**: Verify ActiveMQ connection settings

**Issue**: Circuit breaker not opening
- **Check**: Failure threshold configuration
- **Solution**: Adjust failure count thresholds

**Issue**: Messages stuck in queues
- **Check**: Consumer connection status
- **Solution**: Restart message consumers

**Issue**: Database connection failures
- **Check**: JNDI datasource configuration
- **Solution**: Verify database connectivity

## 📝 Test Results Documentation

### **Test Execution Checklist**
- [ ] Test 1: Basic Message Queue Functionality
- [ ] Test 2: Todo Application Basic Operations
- [ ] Test 3: Asynchronous Message Processing
- [ ] Test 4: Database Resilience - Circuit Breaker
- [ ] Test 5: Retry Mechanism Testing
- [ ] Test 6: Dead Letter Queue Handling
- [ ] Test 7: Performance Under Load
- [ ] Test 8: System Recovery Scenarios

### **Success Criteria**
- ✅ All basic operations work correctly
- ✅ Messages are processed asynchronously
- ✅ Circuit breaker protects against database failures
- ✅ System recovers gracefully from failures
- ✅ No message loss occurs during normal operations
- ✅ Performance remains acceptable under load

## 🎉 Validation Complete

Once all tests pass, the ActiveMQ integration provides:
- **Asynchronous Processing**: Operations don't block on database issues
- **Resilience**: Circuit breaker prevents cascade failures
- **Reliability**: Retry mechanisms handle transient failures
- **Observability**: Comprehensive monitoring and dashboards
- **Scalability**: Message queues enable horizontal scaling

---

**🔧 For manual testing, use the provided web interfaces and monitor the system behavior through the various dashboards and consoles.**
