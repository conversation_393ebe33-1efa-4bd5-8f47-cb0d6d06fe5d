<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <display-name>Todo List Application</display-name>
    <description>Simple Todo List Application with Oracle Database Integration</description>

    <!-- Welcome file list -->
    <welcome-file-list>
        <welcome-file>todos.jsp</welcome-file>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- Session configuration -->
    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <secure>false</secure>
            <name>TODOSESSIONID</name>
            <path>/todo-list-app</path>
        </cookie-config>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>

    <!-- Error pages -->
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.jsp</location>
    </error-page>
    
    <error-page>
        <error-code>500</error-code>
        <location>/error/500.jsp</location>
    </error-page>

    <!-- JNDI Resource Reference for Oracle Database -->
    <resource-ref>
        <description>Oracle Database Connection</description>
        <res-ref-name>jdbc/OracleDS</res-ref-name>
        <res-type>javax.sql.DataSource</res-type>
        <res-auth>Container</res-auth>
        <res-sharing-scope>Shareable</res-sharing-scope>
    </resource-ref>

    <!-- Security roles (for future enhancement) -->
    <security-role>
        <role-name>user</role-name>
    </security-role>

    <!-- Context parameters -->
    <context-param>
        <param-name>application.name</param-name>
        <param-value>Todo List Application</param-value>
    </context-param>
    
    <context-param>
        <param-name>application.version</param-name>
        <param-value>1.0.0</param-value>
    </context-param>

    <!-- Filters -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>com.todoapp.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Servlets -->
    <servlet>
        <servlet-name>TodoController</servlet-name>
        <servlet-class>com.todoapp.controller.TodoController</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>TodoController</servlet-name>
        <url-pattern>/todos</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>TodoController</servlet-name>
        <url-pattern>/todos/*</url-pattern>
    </servlet-mapping>

    <!-- Listeners -->
    <listener>
        <listener-class>com.todoapp.listener.ApplicationContextListener</listener-class>
    </listener>

</web-app>
