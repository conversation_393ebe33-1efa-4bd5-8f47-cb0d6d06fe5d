-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Add password_hash column to users table
ALTER TABLE users ADD password_hash VARCHAR2(255);

-- Get the demo user's password hash (assuming demo user exists)
VARIABLE demo_password_hash VARCHAR2(255);
BEGIN
  SELECT password_hash INTO :demo_password_hash 
  FROM users 
  WHERE username = 'demo';
  DBMS_OUTPUT.PUT_LINE('Demo user password hash: ' || :demo_password_hash);
EXCEPTION
  WHEN NO_DATA_FOUND THEN
    DBMS_OUTPUT.PUT_LINE('Demo user not found or no password hash available');
    :demo_password_hash := NULL;
END;
/

-- Update johndo<PERSON>'s password to match demo user's password
-- (Only if demo user has a password hash)
BEGIN
  IF :demo_password_hash IS NOT NULL THEN
    UPDATE users 
    SET password_hash = :demo_password_hash 
    WHERE username = 'johndo<PERSON>';
    
    IF SQL%ROWCOUNT > 0 THEN
      DBMS_OUTPUT.PUT_LINE('Successfully updated johndoe password hash');
      COMMIT;
    ELSE
      DBMS_OUTPUT.PUT_LINE('johndoe user not found');
    END IF;
  ELSE
    DBMS_OUTPUT.PUT_LINE('Cannot update - demo user password hash not available');
  END IF;
END;
/

-- Verify the update
SELECT username, password_hash 
FROM users 
WHERE username IN ('demo', 'johndoe');

EXIT;
