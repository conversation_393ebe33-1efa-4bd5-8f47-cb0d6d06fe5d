# PowerShell script to generate PDF files from Markdown documents
# Requires pandoc to be installed

param(
    [switch]$InstallPandoc,
    [switch]$GenerateOnly
)

Write-Host "=== WebSphere Documentation PDF Generator ===" -ForegroundColor Green
Write-Host

# Function to check if pandoc is installed
function Test-PandocInstalled {
    try {
        $pandocVersion = pandoc --version 2>$null
        if ($pandocVersion) {
            Write-Host "✅ Pandoc is installed" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ Pandoc is not installed" -ForegroundColor Red
        return $false
    }
    return $false
}

# Function to install pandoc
function Install-Pandoc {
    Write-Host "Installing Pandoc..." -ForegroundColor Yellow
    
    if (Get-Command winget -ErrorAction SilentlyContinue) {
        Write-Host "Using winget to install Pandoc..." -ForegroundColor Yellow
        winget install --id JohnMacFarlane.Pandoc
    }
    elseif (Get-Command choco -ErrorAction SilentlyContinue) {
        Write-Host "Using Chocolatey to install Pandoc..." -ForegroundColor Yellow
        choco install pandoc -y
    }
    else {
        Write-Host "❌ Neither winget nor Chocolatey found." -ForegroundColor Red
        Write-Host "Please install Pandoc manually from: https://pandoc.org/installing.html" -ForegroundColor Yellow
        Write-Host "Or install Chocolatey first: https://chocolatey.org/install" -ForegroundColor Yellow
        return $false
    }
    
    # Verify installation
    Start-Sleep -Seconds 3
    return Test-PandocInstalled
}

# Function to generate PDF from Markdown
function Convert-MarkdownToPdf {
    param(
        [string]$InputFile,
        [string]$OutputFile,
        [string]$Title
    )
    
    Write-Host "Converting $InputFile to PDF..." -ForegroundColor Yellow
    
    try {
        # Pandoc command with enhanced formatting
        $pandocArgs = @(
            $InputFile
            "-o", $OutputFile
            "--pdf-engine=wkhtmltopdf"
            "--metadata", "title=$Title"
            "--metadata", "author=WebSphere Project Team"
            "--metadata", "date=$(Get-Date -Format 'MMMM yyyy')"
            "--toc"
            "--toc-depth=3"
            "--number-sections"
            "--highlight-style=github"
            "--variable", "geometry:margin=1in"
            "--variable", "fontsize=11pt"
            "--variable", "documentclass=article"
            "--variable", "colorlinks=true"
            "--variable", "linkcolor=blue"
            "--variable", "urlcolor=blue"
            "--variable", "toccolor=black"
        )
        
        # Try with wkhtmltopdf first
        & pandoc @pandocArgs 2>$null
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "wkhtmltopdf not found, trying with default engine..." -ForegroundColor Yellow
            
            # Fallback to default PDF engine
            $pandocArgs = @(
                $InputFile
                "-o", $OutputFile
                "--metadata", "title=$Title"
                "--metadata", "author=WebSphere Project Team"
                "--metadata", "date=$(Get-Date -Format 'MMMM yyyy')"
                "--toc"
                "--toc-depth=3"
                "--number-sections"
                "--highlight-style=github"
                "--variable", "geometry:margin=1in"
                "--variable", "fontsize=11pt"
                "--variable", "documentclass=article"
            )
            
            & pandoc @pandocArgs
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Successfully generated: $OutputFile" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Failed to generate PDF: $OutputFile" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error generating PDF: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
try {
    # Check if pandoc is installed
    if (-not (Test-PandocInstalled)) {
        if ($InstallPandoc) {
            if (-not (Install-Pandoc)) {
                Write-Host "❌ Failed to install Pandoc. Exiting." -ForegroundColor Red
                exit 1
            }
        }
        else {
            Write-Host "❌ Pandoc is required to generate PDFs." -ForegroundColor Red
            Write-Host "Run this script with -InstallPandoc to install it automatically." -ForegroundColor Yellow
            Write-Host "Or install manually from: https://pandoc.org/installing.html" -ForegroundColor Yellow
            exit 1
        }
    }
    
    # Define documents to convert
    $documents = @(
        @{
            Input = "WebSphere-Oracle-Setup-Guide.md"
            Output = "WebSphere-Oracle-Setup-Guide.pdf"
            Title = "WebSphere Application Server with Oracle Database - Complete Setup Guide"
        },
        @{
            Input = "WebSphere-Project-Status-Report.md"
            Output = "WebSphere-Project-Status-Report.pdf"
            Title = "WebSphere Oracle Integration Project - Status Report"
        }
    )
    
    $successCount = 0
    $totalCount = $documents.Count
    
    Write-Host "Generating PDF documents..." -ForegroundColor Green
    Write-Host
    
    foreach ($doc in $documents) {
        if (Test-Path $doc.Input) {
            if (Convert-MarkdownToPdf -InputFile $doc.Input -OutputFile $doc.Output -Title $doc.Title) {
                $successCount++
            }
        }
        else {
            Write-Host "❌ Source file not found: $($doc.Input)" -ForegroundColor Red
        }
    }
    
    Write-Host
    Write-Host "=== PDF Generation Summary ===" -ForegroundColor Green
    Write-Host "Successfully generated: $successCount/$totalCount PDFs" -ForegroundColor Green
    
    if ($successCount -eq $totalCount) {
        Write-Host "✅ All PDF documents generated successfully!" -ForegroundColor Green
        Write-Host
        Write-Host "Generated files:" -ForegroundColor Yellow
        foreach ($doc in $documents) {
            if (Test-Path $doc.Output) {
                $fileSize = [math]::Round((Get-Item $doc.Output).Length / 1MB, 2)
                Write-Host "  📄 $($doc.Output) ($fileSize MB)" -ForegroundColor White
            }
        }
    }
    else {
        Write-Host "⚠️  Some PDF generation failed. Check the errors above." -ForegroundColor Yellow
    }
    
    Write-Host
    Write-Host "📚 Documentation files ready for distribution!" -ForegroundColor Green
}
catch {
    Write-Host "❌ Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Alternative method using HTML conversion if PDF fails
Write-Host
Write-Host "💡 Alternative: Generate HTML versions for easy viewing" -ForegroundColor Cyan
Write-Host "Run: pandoc WebSphere-Oracle-Setup-Guide.md -o WebSphere-Oracle-Setup-Guide.html --standalone --toc" -ForegroundColor Gray
Write-Host "Run: pandoc WebSphere-Project-Status-Report.md -o WebSphere-Project-Status-Report.html --standalone --toc" -ForegroundColor Gray
