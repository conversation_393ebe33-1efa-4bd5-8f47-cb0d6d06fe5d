-- =============================================================================
-- TO-DO LIST APPLICATION - DATABASE SCHEMA
-- =============================================================================
-- Creates the todos table in the existing Oracle database schema
-- =============================================================================

SET SERVEROUTPUT ON SIZE 1000000
SET ECHO ON

PROMPT =============================================================================;
PROMPT CREATING TO-DO LIST TABLE IN SYSTEM SCHEMA
PROMPT =============================================================================;

-- Connect to the system schema (same as SavvySpend)
CONNECT system/SavvySpend123@localhost:1521/XE;

-- Create the todos table
CREATE TABLE todos (
    todo_id NUMBER(19) NOT NULL,
    title VARCHAR2(255) NOT NULL,
    description CLOB,
    completed NUMBER(1) DEFAULT 0 NOT NULL,
    priority VARCHAR2(10) DEFAULT 'MEDIUM' NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    due_date DATE,
    user_id NUMBER(19) DEFAULT 1 NOT NULL,
    CONSTRAINT pk_todos PRIMARY KEY (todo_id),
    CONSTRAINT chk_completed CHECK (completed IN (0, 1)),
    CONSTRAINT chk_priority CHECK (priority IN ('LOW', 'MEDIUM', 'HIGH')),
    CONSTRAINT chk_title_not_empty CHECK (LENGTH(TRIM(title)) > 0)
);

-- Create sequence for auto-incrementing todo_id
CREATE SEQUENCE todo_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Create trigger for auto-incrementing todo_id
CREATE OR REPLACE TRIGGER trg_todos_id
    BEFORE INSERT ON todos
    FOR EACH ROW
BEGIN
    IF :NEW.todo_id IS NULL THEN
        :NEW.todo_id := todo_seq.NEXTVAL;
    END IF;
END;
/

-- Create trigger for updating updated_date
CREATE OR REPLACE TRIGGER trg_todos_updated_date
    BEFORE UPDATE ON todos
    FOR EACH ROW
BEGIN
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Create indexes for better performance
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_user_id ON todos(user_id);
CREATE INDEX idx_todos_created_date ON todos(created_date);
CREATE INDEX idx_todos_priority ON todos(priority);

-- Insert sample data
INSERT INTO todos (title, description, priority, due_date) VALUES 
('Complete project documentation', 'Write comprehensive documentation for the new to-do application', 'HIGH', SYSDATE + 3);

INSERT INTO todos (title, description, priority, completed) VALUES 
('Review code changes', 'Review and approve pending pull requests', 'MEDIUM', 0);

INSERT INTO todos (title, description, priority, completed) VALUES 
('Setup development environment', 'Install and configure all necessary development tools', 'LOW', 1);

INSERT INTO todos (title, description, priority, due_date) VALUES 
('Prepare presentation', 'Create slides for the quarterly review meeting', 'HIGH', SYSDATE + 7);

INSERT INTO todos (title, description, priority) VALUES 
('Update dependencies', 'Update all project dependencies to latest versions', 'MEDIUM');

-- Commit the changes
COMMIT;

PROMPT;
PROMPT =============================================================================;
PROMPT TO-DO LIST TABLE CREATED SUCCESSFULLY!;
PROMPT =============================================================================;
PROMPT;
PROMPT Table: todos;
PROMPT Columns:;
PROMPT - todo_id (Primary Key, Auto-increment);
PROMPT - title (Required, max 255 chars);
PROMPT - description (Optional, CLOB);
PROMPT - completed (0=Pending, 1=Completed);
PROMPT - priority (LOW, MEDIUM, HIGH);
PROMPT - created_date (Auto-set);
PROMPT - updated_date (Auto-updated);
PROMPT - due_date (Optional);
PROMPT - user_id (Default: 1);
PROMPT;
PROMPT Sample data inserted: 5 to-do items;
PROMPT =============================================================================;
