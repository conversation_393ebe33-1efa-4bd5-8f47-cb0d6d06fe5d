#!/bin/bash
# Test script for clean WebSphere setup

echo "=== Testing Clean WebSphere Setup ==="
echo

# Test 1: Check container status
echo "1. Checking container status..."
CONTAINER_STATUS=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep websphere-clean)
if [ -n "$CONTAINER_STATUS" ]; then
    echo "✅ WebSphere container is running"
    echo "   $CONTAINER_STATUS"
else
    echo "❌ WebSphere container is not running"
    echo "Please start with: docker-compose up -d"
    exit 1
fi
echo

# Test 2: Check admin console accessibility
echo "2. Testing admin console accessibility..."
if command -v curl >/dev/null 2>&1; then
    CONSOLE_RESPONSE=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost:9043/ibm/console)
    if [ "$CONSOLE_RESPONSE" = "200" ] || [ "$CONSOLE_RESPONSE" = "302" ]; then
        echo "✅ Admin console is accessible (HTTP $CONSOLE_RESPONSE)"
    else
        echo "❌ Admin console is not accessible (HTTP $CONSOLE_RESPONSE)"
    fi
else
    echo "ℹ️  curl not available - cannot test admin console port"
fi
echo

# Test 3: Check if WebSphere is fully started
echo "3. Checking WebSphere startup status..."
STARTUP_CHECK=$(docker exec websphere-clean bash -c "grep 'open for e-business' /opt/IBM/WebSphere/AppServer/profiles/AppSrv01/logs/server1/SystemOut.log | tail -1" 2>/dev/null)
if [ -n "$STARTUP_CHECK" ]; then
    echo "✅ WebSphere is fully started"
    echo "   $STARTUP_CHECK"
else
    echo "⏳ WebSphere is still starting up..."
    echo "   Please wait a few more minutes and try again"
fi
echo

# Test 4: Test admin credentials via wsadmin
echo "4. Testing admin credentials..."
CRED_TEST=$(docker exec websphere-clean /opt/IBM/WebSphere/AppServer/bin/wsadmin.sh \
    -conntype SOAP -host localhost -port 8880 \
    -user wsadmin -password was@123 \
    -c "print('CREDENTIALS_WORKING')" 2>/dev/null | grep "CREDENTIALS_WORKING")

if [ -n "$CRED_TEST" ]; then
    echo "✅ Admin credentials are working"
else
    echo "⚠️  Admin credentials test inconclusive (may need SSL cert acceptance)"
    echo "   Try logging in via web console: https://localhost:9043/ibm/console"
fi
echo

# Test 5: Check for authentication errors
echo "5. Checking for recent authentication errors..."
AUTH_ERRORS=$(docker exec websphere-clean bash -c "tail -50 /opt/IBM/WebSphere/AppServer/profiles/AppSrv01/logs/server1/SystemOut.log | grep -i 'authentication error\|SECJ0118E'" 2>/dev/null)
if [ -z "$AUTH_ERRORS" ]; then
    echo "✅ No recent authentication errors found"
else
    echo "❌ Authentication errors detected:"
    echo "$AUTH_ERRORS"
fi
echo

# Summary
echo "=== Test Summary ==="
echo "🌐 Admin Console: https://localhost:9043/ibm/console"
echo "👤 Username: wsadmin"
echo "🔑 Password: was@123"
echo
echo "If all tests passed, your clean WebSphere setup is ready!"
echo "If any tests failed, check the logs with:"
echo "  docker-compose logs websphere"
