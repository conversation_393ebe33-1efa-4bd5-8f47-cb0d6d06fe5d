-- Check expenses and budgets table structures
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Check if tables exist
SELECT table_name FROM user_tables WHERE table_name IN ('EXPENSES', 'BUDGETS', 'CATEGORIES');

-- Describe expenses table
SELECT 'EXPENSES table structure:' AS info FROM dual;
DESCRIBE expenses;

-- Describe budgets table  
SELECT 'BUDGETS table structure:' AS info FROM dual;
DESCRIBE budgets;

-- Describe categories table
SELECT 'CATEGORIES table structure:' AS info FROM dual;
DESCRIBE categories;

-- Check demo user ID
SELECT 'Demo user ID:' AS info FROM dual;
SELECT id FROM users WHERE username = 'demo';

-- Check existing data
SELECT 'Existing expenses count:' AS info FROM dual;
SELECT COUNT(*) FROM expenses;

SELECT 'Existing budgets count:' AS info FROM dual;
SELECT COUNT(*) FROM budgets;

SELECT 'Existing categories count:' AS info FROM dual;
SELECT COUNT(*) FROM categories;

EXIT;
