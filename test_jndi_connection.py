#!/usr/bin/env python
# Test JNDI DataSource Connection

print("=== Testing JNDI DataSource Connection ===")

try:
    import javax.naming as naming
    import javax.sql as sql
    
    # Create initial context
    ctx = naming.InitialContext()
    print("✅ InitialContext created successfully")
    
    # Lookup JNDI datasource
    ds = ctx.lookup('jdbc/OracleDS')
    print("✅ JNDI lookup successful: jdbc/OracleDS")
    
    # Get database connection
    conn = ds.getConnection()
    print("✅ Database connection successful")
    
    # Test query to check BUDGETS table
    stmt = conn.createStatement()
    rs = stmt.executeQuery("SELECT COUNT(*) FROM BUDGETS")
    
    if rs.next():
        count = rs.getInt(1)
        print("✅ BUDGETS table found with " + str(count) + " records")
    
    # Test other tables
    tables = ['USERS', 'CATEGORIES', 'EXPENSES', 'GOALS']
    for table in tables:
        try:
            rs2 = stmt.executeQuery("SELECT COUNT(*) FROM " + table)
            if rs2.next():
                count2 = rs2.getInt(1)
                print("✅ " + table + " table found with " + str(count2) + " records")
            rs2.close()
        except Exception as e:
            print("❌ Error with table " + table + ": " + str(e))
    
    # Clean up
    rs.close()
    stmt.close()
    conn.close()
    
    print("")
    print("=== JNDI Connection Test Results ===")
    print("✅ JNDI DataSource: Working")
    print("✅ Database Connection: Working")
    print("✅ All required tables: Found")
    print("")
    print("🎯 JNDI configuration is correct!")
    print("🔍 The issue must be elsewhere...")
    
except Exception as e:
    print("❌ Error occurred: " + str(e))
    import traceback
    traceback.print_exc()
    
    print("")
    print("=== Troubleshooting Suggestions ===")
    print("1. Check if WebSphere server is fully started")
    print("2. Verify JNDI datasource configuration")
    print("3. Check database connectivity")
    print("4. Review authentication settings")

print("=== JNDI Connection Test Complete ===")
