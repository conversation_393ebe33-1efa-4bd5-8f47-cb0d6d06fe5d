-- Sample Database Initialization Script
-- Creates sample tables for testing WebSphere-Oracle connectivity

-- Connect to the pluggable database
ALTER SESSION SET CONTAINER = XEPDB1;

-- Create a sample user for application
CREATE USER savvyspend IDENTIFIED BY SavvySpend123
DEFAULT TABLESPACE USERS
TEMPORARY TABLESPACE TEMP;

-- Grant necessary privileges
GRANT CONNECT, RESOURCE TO savvyspend;
GRANT CREATE SESSION TO savvyspend;
GRANT CREATE TABLE TO savvyspend;
GRANT CREATE SEQUENCE TO savvyspend;
GRANT CREATE VIEW TO savvyspend;

-- Grant quota on USERS tablespace
ALTER USER savvyspend QUOTA UNLIMITED ON USERS;

-- Connect as the new user
CONNECT savvyspend/SavvySpend123@XEPDB1;

-- Create sample tables
CREATE TABLE users (
    user_id NUMBER PRIMARY KEY,
    username VARCHAR2(50) NOT NULL UNIQUE,
    email VARCHAR2(100) NOT NULL,
    created_date DATE DEFAULT SYSDATE
);

CREATE TABLE transactions (
    transaction_id NUMBER PRIMARY KEY,
    user_id NUMBER REFERENCES users(user_id),
    amount NUMBER(10,2) NOT NULL,
    description VARCHAR2(200),
    transaction_date DATE DEFAULT SYSDATE
);

-- Create sequences
CREATE SEQUENCE user_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE transaction_seq START WITH 1 INCREMENT BY 1;

-- Insert sample data
INSERT INTO users (user_id, username, email) VALUES (user_seq.NEXTVAL, 'testuser', '<EMAIL>');
INSERT INTO users (user_id, username, email) VALUES (user_seq.NEXTVAL, 'admin', '<EMAIL>');

INSERT INTO transactions (transaction_id, user_id, amount, description) 
VALUES (transaction_seq.NEXTVAL, 1, 100.50, 'Sample transaction 1');
INSERT INTO transactions (transaction_id, user_id, amount, description) 
VALUES (transaction_seq.NEXTVAL, 1, -25.00, 'Sample transaction 2');

-- Commit the changes
COMMIT;

-- Create a test view
CREATE VIEW user_transactions AS
SELECT u.username, u.email, t.amount, t.description, t.transaction_date
FROM users u
JOIN transactions t ON u.user_id = t.user_id;

-- Display success message
SELECT 'Sample database initialized successfully' as status FROM dual;
