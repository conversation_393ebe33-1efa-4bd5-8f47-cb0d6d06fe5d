# 📋 Todo List Application

A simple, elegant todo list application built with Java servlets, JSP, and Oracle Database integration. Designed for deployment on IBM WebSphere Application Server.

## 🎯 Features

### ✅ Core Functionality
- **Add Todos**: Create new tasks with title, description, priority, and due date
- **Edit Todos**: Update existing tasks with full editing capabilities
- **Complete/Uncomplete**: Toggle task completion status with one click
- **Delete Todos**: Remove tasks you no longer need
- **Filter Views**: View all tasks, pending only, or completed only

### 🎨 User Interface
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Modern UI**: Clean, professional interface with Bootstrap 5.3.0
- **Interactive Elements**: Smooth animations and hover effects
- **Priority Indicators**: Color-coded priority badges (High, Medium, Low)
- **Status Tracking**: Visual indicators for completed vs pending tasks
- **Due Date Alerts**: Overdue tasks are highlighted in red

### 📊 Dashboard & Analytics
- **Statistics Cards**: Total, completed, pending, and overdue task counts
- **Progress Bar**: Visual representation of overall completion percentage
- **Real-time Updates**: Statistics update automatically as you manage tasks

### 🔧 Technical Features
- **Oracle Database**: Full integration with Oracle Database via JNDI
- **WebSphere Compatible**: Designed specifically for IBM WebSphere Application Server
- **AJAX Operations**: Smooth, no-page-reload interactions
- **Error Handling**: Comprehensive error pages and user feedback
- **UTF-8 Support**: Full internationalization support

## 🏗️ Architecture

### **Backend Components**
```
com/todoapp/
├── entity/
│   └── Todo.java              # Todo entity with utility methods
├── service/
│   └── TodoService.java       # Business logic and database operations
├── controller/
│   └── TodoController.java    # Servlet controller for HTTP requests
├── filter/
│   └── CharacterEncodingFilter.java  # UTF-8 encoding filter
└── listener/
    └── ApplicationContextListener.java  # Application lifecycle management
```

### **Frontend Components**
```
webapp/
├── todos.jsp                  # Main todo list page
├── index.jsp                  # Landing page (redirects to todos)
├── error/
│   ├── 404.jsp               # Page not found error
│   └── 500.jsp               # Server error page
└── WEB-INF/
    └── web.xml               # Web application configuration
```

### **Database Schema**
```sql
CREATE TABLE todos (
    todo_id NUMBER(19) PRIMARY KEY,
    title VARCHAR2(255) NOT NULL,
    description CLOB,
    completed NUMBER(1) DEFAULT 0,
    priority VARCHAR2(10) DEFAULT 'MEDIUM',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    due_date DATE,
    user_id NUMBER(19) DEFAULT 1
);
```

## 🚀 Quick Start

### **Prerequisites**
- Java 8 or higher
- Maven 3.6 or higher
- Oracle Database (running in Docker container)
- IBM WebSphere Application Server
- Docker and Docker Compose

### **1. Setup Database**
```bash
# Start Oracle database
docker-compose up -d oracle-db

# Wait for database to be ready (check logs)
docker logs oracle

# Create todos table
docker exec -it oracle sqlplus system/SavvySpend123@XE @/opt/oracle/scripts/startup/create_todos_table.sql
```

### **2. Build Application**
```bash
cd todoapp
chmod +x build.sh
./build.sh
```

### **3. Deploy to WebSphere**
```bash
cd ../websphere/deployedapp
chmod +x deploy-todo-app.sh
./deploy-todo-app.sh
```

### **4. Access Application**
Open your browser and navigate to:
```
http://localhost:9080/todo-list-app/todos
```

## 📱 Usage Guide

### **Adding a New Todo**
1. Click the "Add Todo" button in the navigation or empty state
2. Fill in the todo details:
   - **Title**: Required field for the task name
   - **Description**: Optional detailed description
   - **Priority**: Choose from Low, Medium, or High
   - **Due Date**: Optional deadline for the task
3. Click "Save Todo" to create the task

### **Managing Existing Todos**
- **Complete/Uncomplete**: Click the checkmark/undo button next to any task
- **Edit**: Click the edit (pencil) icon to modify task details
- **Delete**: Click the trash icon to remove a task (with confirmation)

### **Filtering Tasks**
Use the filter tabs to view:
- **All Tasks**: Complete overview of all todos
- **Pending**: Only incomplete tasks
- **Completed**: Only finished tasks

### **Understanding the Dashboard**
- **Total Tasks**: Overall count of all todos
- **Completed**: Number of finished tasks
- **Pending**: Number of incomplete tasks
- **Overdue**: Number of tasks past their due date
- **Progress Bar**: Visual completion percentage

## 🔧 Configuration

### **Database Connection**
The application uses JNDI for database connectivity. Ensure your WebSphere server has:
- **JNDI Name**: `jdbc/OracleDS`
- **Database URL**: `***********************************`
- **Username**: `system`
- **Password**: `SavvySpend123`

### **Application Settings**
Key configuration in `web.xml`:
- **Context Root**: `/todo-list-app`
- **Session Timeout**: 30 minutes
- **Character Encoding**: UTF-8
- **Error Pages**: Custom 404 and 500 pages

## 🛠️ Development

### **Project Structure**
```
todoapp/
├── src/main/
│   ├── java/com/todoapp/     # Java source code
│   └── webapp/               # Web resources
├── target/                   # Build output
├── pom.xml                   # Maven configuration
├── build.sh                  # Build script
└── README.md                 # This file
```

### **Building from Source**
```bash
# Clean and compile
mvn clean compile

# Run tests
mvn test

# Package WAR file
mvn package

# Or use the build script
./build.sh
```

### **Customization**
- **Styling**: Modify CSS in `todos.jsp` for custom themes
- **Database**: Update `TodoService.java` for different database schemas
- **Features**: Extend `TodoController.java` for additional functionality

## 🔍 Troubleshooting

### **Common Issues**

**Application won't start:**
- Check WebSphere logs: `docker logs appserver`
- Verify Oracle database is running: `docker ps | grep oracle`
- Ensure JNDI datasource is configured correctly

**Database connection errors:**
- Verify Oracle container is healthy: `docker exec -it oracle sqlplus system/SavvySpend123@XE`
- Check if todos table exists: `SELECT * FROM todos;`
- Ensure WebSphere can reach Oracle container

**404 errors:**
- Verify application is deployed: Check WebSphere Admin Console
- Confirm context root: Should be `/todo-list-app`
- Check application status in WebSphere

### **Useful Commands**
```bash
# Check application logs
docker logs appserver

# Access Oracle database
docker exec -it oracle sqlplus system/SavvySpend123@XE

# Restart WebSphere
docker restart appserver

# View deployed applications
# Access WebSphere Admin Console at http://localhost:9043/ibm/console
```

## 📄 License

This project is created for educational and demonstration purposes. Feel free to use and modify as needed.

## 🤝 Contributing

This is a demonstration application. For improvements or bug fixes:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Happy Task Managing! 📋✅**
