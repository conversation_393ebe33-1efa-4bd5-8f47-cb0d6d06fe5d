#!/usr/bin/env python
# Fix existing JNDI DataSource to point to XE container database

print("=== Fixing JNDI DataSource for SavvySpend Application ===")

try:
    # Find the existing DataSource
    dataSourceList = AdminConfig.list('DataSource')
    print("Available DataSources:")
    print(dataSourceList)
    
    # Find the OracleDataSource
    oracleDS = None
    for ds in dataSourceList.split('\n'):
        if ds and 'OracleDataSource' in ds:
            oracleDS = ds.strip()
            break
    
    if not oracleDS:
        print("❌ Error: Could not find OracleDataSource")
        exit(1)
    
    print("Found OracleDataSource: " + str(oracleDS))
    
    # Get the current URL property
    propertySet = AdminConfig.showAttribute(oracleDS, 'propertySet')
    print("Property Set: " + str(propertySet))
    
    # Find the URL property
    properties = AdminConfig.list('J2EEResourceProperty', propertySet)
    urlProperty = None
    
    for prop in properties.split('\n'):
        if prop:
            propName = AdminConfig.showAttribute(prop.strip(), 'name')
            if propName == 'URL':
                urlProperty = prop.strip()
                break
    
    if not urlProperty:
        print("❌ Error: Could not find URL property")
        exit(1)
    
    # Get current URL value
    currentURL = AdminConfig.showAttribute(urlProperty, 'value')
    print("Current URL: " + str(currentURL))
    
    # Update URL to point to XE container database instead of XEPDB1
    newURL = "***********************************"
    print("New URL: " + newURL)
    
    # Modify the URL property
    AdminConfig.modify(urlProperty, [['value', newURL]])
    print("✅ URL property updated successfully")
    
    # Save the configuration
    print("Saving configuration...")
    AdminConfig.save()
    print("✅ Configuration saved successfully")
    
    print("")
    print("=== JNDI DataSource Fix Summary ===")
    print("✅ DataSource: OracleDataSource")
    print("✅ JNDI Name: jdbc/OracleDS")
    print("✅ Old URL: " + str(currentURL))
    print("✅ New URL: " + newURL)
    print("✅ Database: XE container database (where tables exist)")
    print("")
    print("🔄 Please restart the WebSphere server to apply changes:")
    print("   docker restart appserver")
    print("")
    print("📝 Update SavvySpend application to use existing JNDI:")
    print("   Remove: spring.datasource.url, spring.datasource.username, spring.datasource.password")
    print("   Add: spring.datasource.jndi-name=jdbc/OracleDS")
    
except Exception as e:
    print("❌ Error occurred: " + str(e))
    import traceback
    traceback.print_exc()
    exit(1)

print("=== JNDI DataSource Fix Complete ===")
