# D:\Docker\webapps\ai-news-aggregator\websphere\nginx\nginx.conf
worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout 65;

    server {
        listen 80; # Nginx listens on host port 80
        server_name localhost;

        # Serve static files from the /usr/share/nginx/html directory inside the container.
        # This directory is mounted from your host's 'nginx/html' folder.
        root /usr/share/nginx/html;
        index index.html index.htm;

        # This location block first tries to serve a static file or a directory.
        # If not found, it passes the request to the @proxy_to_haproxy block.
        location / {
            try_files $uri $uri/ @proxy_to_haproxy;
        }

        # This block defines how Nginx proxies requests to HAProxy.
        # It's used for dynamic content not found as static files.
        location @proxy_to_haproxy {
            # Proxy to the HAProxy container via its container name and internal port.
            proxy_pass http://haproxy:80; # H<PERSON>roxy is listening on internal port 80
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 600; # Increase timeouts for WebSphere applications
            proxy_send_timeout 600;
            proxy_read_timeout 600;
            send_timeout 600;
        }

        # OPTIONAL: If your WebSphere application has a specific context root (e.g., /your_app_context)
        # and you want to explicitly proxy only those requests, you could do:
        # location /your_app_context/ {
        #     proxy_pass http://haproxy-loadbalancer:80/your_app_context/;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        #     proxy_connect_timeout 600;
        #     proxy_send_timeout 600;
        #     proxy_read_timeout 600;
        #     send_timeout 600;
        # }
    }
}