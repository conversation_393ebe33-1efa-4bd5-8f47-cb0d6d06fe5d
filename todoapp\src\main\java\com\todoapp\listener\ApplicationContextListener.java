package com.todoapp.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.util.logging.Logger;

/**
 * Application Context Listener
 * Handles application startup and shutdown events
 */
public class ApplicationContextListener implements ServletContextListener {
    private static final Logger logger = Logger.getLogger(ApplicationContextListener.class.getName());

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        logger.info("Todo List Application started successfully");
        
        // Set application attributes
        sce.getServletContext().setAttribute("app.name", "Todo List Application");
        sce.getServletContext().setAttribute("app.version", "1.0.0");
        sce.getServletContext().setAttribute("app.startTime", System.currentTimeMillis());
        
        // Log application information
        String appName = sce.getServletContext().getInitParameter("application.name");
        String appVersion = sce.getServletContext().getInitParameter("application.version");
        
        logger.info("Application Name: " + appName);
        logger.info("Application Version: " + appVersion);
        logger.info("Context Path: " + sce.getServletContext().getContextPath());
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        logger.info("Todo List Application is shutting down");
        
        // Cleanup resources if needed
        Long startTime = (Long) sce.getServletContext().getAttribute("app.startTime");
        if (startTime != null) {
            long uptime = System.currentTimeMillis() - startTime;
            logger.info("Application uptime: " + (uptime / 1000) + " seconds");
        }
    }
}
