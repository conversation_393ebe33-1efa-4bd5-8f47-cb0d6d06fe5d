-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Declare variables to store user IDs
VARIABLE johndoe_id NUMBER;
VARIABLE janedoe_id NUMBER;
VARIABLE bobsmith_id NUMBER;

-- Insert additional users and capture their IDs
INSERT INTO users (user_id, username, email)
VALUES (user_seq.NEXTVAL, 'johndoe', '<EMAIL>')
RETURNING user_id INTO :johndoe_id;

INSERT INTO users (user_id, username, email)
VALUES (user_seq.NEXTVAL, 'janedoe', '<EMAIL>')
RETURNING user_id INTO :janedoe_id;

INSERT INTO users (user_id, username, email)
VALUES (user_seq.NEXTVAL, 'bobsmith', '<EMAIL>')
RETURNING user_id INTO :bobsmith_id;

-- Insert additional transactions using the actual user IDs
INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, :johndoe_id, 75.25, 'Grocery shopping');

INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, :johndoe_id, -120.00, 'Monthly subscription');

INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, :janedoe_id, 250.00, 'Freelance payment');

INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, :janedoe_id, -85.50, 'Dining out');

INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, :bobsmith_id, 1200.00, 'Salary deposit');

INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, :bobsmith_id, -450.00, 'Rent payment');

-- Commit the changes
COMMIT;

-- Display the captured user IDs
SELECT 'John Doe user ID: ' || :johndoe_id AS info FROM dual;
SELECT 'Jane Doe user ID: ' || :janedoe_id AS info FROM dual;
SELECT 'Bob Smith user ID: ' || :bobsmith_id AS info FROM dual;

-- Verify data insertion
SELECT 'Additional users added: ' || COUNT(*) AS status
FROM users
WHERE username IN ('johndoe', 'janedoe', 'bobsmith');

SELECT 'Additional transactions added: ' || COUNT(*) AS status
FROM transactions
WHERE description IN ('Grocery shopping', 'Monthly subscription', 'Freelance payment', 'Dining out', 'Salary deposit', 'Rent payment');

EXIT;