#!/bin/bash

# Install ActiveMQ Client Libraries in WebSphere
# This script downloads and installs the necessary ActiveMQ client JARs

echo "=== Installing ActiveMQ Client Libraries in WebSphere ==="

# Configuration
ACTIVEMQ_VERSION="5.18.3"
WEBSPHERE_LIB_DIR="/opt/IBM/WebSphere/AppServer/lib"
TEMP_DIR="/tmp/activemq-client"

# Create temporary directory
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

echo "📦 Downloading ActiveMQ client libraries..."

# Download ActiveMQ client JAR
ACTIVEMQ_CLIENT_URL="https://repo1.maven.org/maven2/org/apache/activemq/activemq-client/${ACTIVEMQ_VERSION}/activemq-client-${ACTIVEMQ_VERSION}.jar"
echo "Downloading: $ACTIVEMQ_CLIENT_URL"
curl -L -o "activemq-client-${ACTIVEMQ_VERSION}.jar" "$ACTIVEMQ_CLIENT_URL"

if [ $? -eq 0 ]; then
    echo "✅ Downloaded activemq-client-${ACTIVEMQ_VERSION}.jar"
else
    echo "❌ Failed to download ActiveMQ client JAR"
    exit 1
fi

# Download required dependencies
echo "📦 Downloading dependencies..."

# SLF4J API
SLF4J_VERSION="1.7.36"
curl -L -o "slf4j-api-${SLF4J_VERSION}.jar" "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/${SLF4J_VERSION}/slf4j-api-${SLF4J_VERSION}.jar"

# SLF4J Simple (for logging)
curl -L -o "slf4j-simple-${SLF4J_VERSION}.jar" "https://repo1.maven.org/maven2/org/slf4j/slf4j-simple/${SLF4J_VERSION}/slf4j-simple-${SLF4J_VERSION}.jar"

# Geronimo JMS Spec
GERONIMO_VERSION="1.1.1"
curl -L -o "geronimo-jms_1.1_spec-${GERONIMO_VERSION}.jar" "https://repo1.maven.org/maven2/org/apache/geronimo/specs/geronimo-jms_1.1_spec/${GERONIMO_VERSION}/geronimo-jms_1.1_spec-${GERONIMO_VERSION}.jar"

# Jackson for JSON processing (used by our messaging classes)
JACKSON_VERSION="2.15.2"
curl -L -o "jackson-core-${JACKSON_VERSION}.jar" "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/${JACKSON_VERSION}/jackson-core-${JACKSON_VERSION}.jar"
curl -L -o "jackson-databind-${JACKSON_VERSION}.jar" "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/${JACKSON_VERSION}/jackson-databind-${JACKSON_VERSION}.jar"
curl -L -o "jackson-annotations-${JACKSON_VERSION}.jar" "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/${JACKSON_VERSION}/jackson-annotations-${JACKSON_VERSION}.jar"
curl -L -o "jackson-datatype-jsr310-${JACKSON_VERSION}.jar" "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/${JACKSON_VERSION}/jackson-datatype-jsr310-${JACKSON_VERSION}.jar"

echo "✅ Downloaded all dependencies"

# List downloaded files
echo "📋 Downloaded files:"
ls -la *.jar

# Copy JARs to WebSphere lib directory
echo "📂 Installing JARs to WebSphere lib directory: $WEBSPHERE_LIB_DIR"

for jar in *.jar; do
    if [ -f "$jar" ]; then
        echo "Installing: $jar"
        cp "$jar" "$WEBSPHERE_LIB_DIR/"
        if [ $? -eq 0 ]; then
            echo "✅ Installed: $jar"
        else
            echo "❌ Failed to install: $jar"
        fi
    fi
done

# Set proper permissions
echo "🔧 Setting permissions..."
chmod 644 "$WEBSPHERE_LIB_DIR"/*.jar

# Create a combined ActiveMQ JAR for easier classpath management
echo "📦 Creating combined ActiveMQ JAR..."
cd "$WEBSPHERE_LIB_DIR"

# Create manifest for combined JAR
cat > activemq-manifest.txt << EOF
Manifest-Version: 1.0
Class-Path: activemq-client-${ACTIVEMQ_VERSION}.jar slf4j-api-${SLF4J_VERSION}.jar slf4j-simple-${SLF4J_VERSION}.jar geronimo-jms_1.1_spec-${GERONIMO_VERSION}.jar jackson-core-${JACKSON_VERSION}.jar jackson-databind-${JACKSON_VERSION}.jar jackson-annotations-${JACKSON_VERSION}.jar jackson-datatype-jsr310-${JACKSON_VERSION}.jar

EOF

# Create the combined JAR (this is just a manifest that references other JARs)
jar cfm activemq-all-${ACTIVEMQ_VERSION}.jar activemq-manifest.txt
rm activemq-manifest.txt

echo "✅ Created combined JAR: activemq-all-${ACTIVEMQ_VERSION}.jar"

# Clean up temporary directory
rm -rf "$TEMP_DIR"

echo ""
echo "=== Installation Summary ==="
echo "✅ ActiveMQ Client: activemq-client-${ACTIVEMQ_VERSION}.jar"
echo "✅ SLF4J Logging: slf4j-api-${SLF4J_VERSION}.jar, slf4j-simple-${SLF4J_VERSION}.jar"
echo "✅ JMS Spec: geronimo-jms_1.1_spec-${GERONIMO_VERSION}.jar"
echo "✅ Jackson JSON: jackson-*-${JACKSON_VERSION}.jar"
echo "✅ Combined JAR: activemq-all-${ACTIVEMQ_VERSION}.jar"
echo ""
echo "📍 Installation Location: $WEBSPHERE_LIB_DIR"
echo ""
echo "🔄 Next Steps:"
echo "1. Restart WebSphere server: docker restart appserver"
echo "2. Run JMS configuration script: configure-jms-resources.py"
echo "3. Verify JMS resources in WebSphere Admin Console"
echo ""
echo "📝 Classpath Reference for JMS Provider:"
echo "   $WEBSPHERE_LIB_DIR/activemq-all-${ACTIVEMQ_VERSION}.jar"

echo "=== ActiveMQ Client Installation Complete ==="
