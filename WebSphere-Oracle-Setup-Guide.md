# WebSphere Application Server with Oracle Database - Complete Setup Guide

## 📋 **Project Overview**

This guide provides step-by-step instructions for setting up a containerized WebSphere Application Server environment with Oracle Database integration, including HAProxy load balancer and Nginx frontend.

### **Architecture Components**
- **WebSphere Application Server** - IBM Traditional WebSphere 9.0.5.x
- **Oracle Database** - Oracle 21c Express Edition (21.3.0-xe)
- **HAProxy** - Load balancer and SSL termination
- **Nginx** - Web server and reverse proxy
- **Docker Compose** - Container orchestration

### **Key Features**
- ✅ Persistent configuration and data
- ✅ Custom admin authentication
- ✅ Oracle JDBC connectivity
- ✅ SSL/HTTPS support
- ✅ Container restart resilience

---

## 🚀 **Quick Start**

### **Prerequisites**
- Docker Desktop installed and running
- 8GB+ RAM available
- 20GB+ disk space
- Windows PowerShell or Linux/Mac terminal

### **1. Clone/Create Project Structure**
```bash
mkdir websphere-oracle-project
cd websphere-oracle-project
mkdir -p websphere/jdbc haproxy nginx/html database/init
```

### **2. Download Required Files**
- Oracle JDBC Driver: `ojdbc8.jar` (Oracle 21.3.0 - Java 8 compatible)
- Oracle Internationalization: `orai18n.jar`

### **3. Quick Deploy**
```bash
# Start all services
docker-compose up -d

# Monitor startup (5-10 minutes)
docker-compose logs -f websphere

# Access admin console when ready
# https://localhost:9043/ibm/console
```

---

## 🔧 **Detailed Setup Instructions**

### **Step 1: Docker Compose Configuration**

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  # Oracle Database
  oracle-db:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    container_name: savvyspend-oracle-clean
    ports:
      - "1521:1521"
      - "5500:5500"
    environment:
      - ORACLE_PDB=XEPDB1
      - ORACLE_PWD=SavvySpend123
      - ORACLE_CHARACTERSET=AL32UTF8
      - TZ=Asia/Kolkata
    volumes:
      - oracle_data_clean:/opt/oracle/oradata
      - ./database/init:/opt/oracle/scripts/startup
    restart: unless-stopped
    networks:
      - webapp-network
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "system/SavvySpend123@//localhost:1521/XE", "@/opt/oracle/scripts/startup/healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # WebSphere Application Server
  websphere:
    image: "ibmcom/websphere-traditional:latest"
    container_name: websphere-clean
    ports:
      - "9080:9080"   # WebSphere HTTP port
      - "9043:9043"   # WebSphere Admin Console (HTTPS)
    environment:
      # Admin user configuration
      - ENABLE_BASIC_LOGGING=true
      - ADMIN_USER_NAME=wsadmin
      - ADMIN_PASSWORD=was@123
      
      # Time zone configuration
      - TZ=Asia/Kolkata
      - JAVA_TIMEZONE=Asia/Kolkata
      
      # WebSphere specific settings
      - WAS_ADMIN_USERNAME=wsadmin
      - WAS_ADMIN_PASSWORD=was@123
      
      # Profile configuration
      - PROFILE_NAME=AppSrv01
      - SERVER_NAME=server1
      - CELL_NAME=DefaultCell01
      - NODE_NAME=DefaultNode01
      
      # Oracle Database Connection
      - ORACLE_HOST=oracle-db
      - ORACLE_PORT=1521
      - ORACLE_SID=XE
      - ORACLE_PDB=XEPDB1
      - ORACLE_USER=system
      - ORACLE_PASSWORD=SavvySpend123
      
    volumes:
      # Use named volumes for WebSphere persistence
      - websphere_profiles_clean:/opt/IBM/WebSphere/AppServer/profiles
      
    restart: always
    depends_on:
      - oracle-db
    networks:
      - webapp-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9080/ || exit 1"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 300s

  # HAProxy Load Balancer
  haproxy:
    build:
      context: ./haproxy
      dockerfile: Dockerfile
    image: custom-haproxy-clean:latest
    container_name: haproxy-clean
    environment:
      - TZ=Asia/Kolkata
    ports:
      - "443:443"     # HTTPS port
    volumes:
      - ./haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    depends_on:
      - websphere
    restart: always
    networks:
      - webapp-network

  # Nginx Web Server
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    image: custom-nginx-clean:latest
    container_name: nginx-clean
    environment:
      - TZ=Asia/Kolkata
    ports:
      - "80:80"       # Main HTTP entry point
    volumes:
      - ./nginx/html:/usr/share/nginx/html:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - haproxy
    restart: always
    networks:
      - webapp-network

networks:
  webapp-network:
    driver: bridge
    name: webapp-network-clean

volumes:
  websphere_profiles_clean:
    driver: local
  oracle_data_clean:
    driver: local
```

### **Step 2: HAProxy Configuration**

Create `haproxy/Dockerfile`:
```dockerfile
FROM haproxy:2.8-alpine
COPY haproxy.cfg /usr/local/etc/haproxy/haproxy.cfg
EXPOSE 443
```

Create `haproxy/haproxy.cfg`:
```
global
    daemon
    log stdout local0 info
    maxconn 4096
    ssl-default-bind-options ssl-min-ver TLSv1.2
    ssl-default-bind-ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384

defaults
    mode http
    log global
    option httplog
    option dontlognull
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    errorfile 400 /usr/local/etc/haproxy/errors/400.http
    errorfile 403 /usr/local/etc/haproxy/errors/403.http
    errorfile 408 /usr/local/etc/haproxy/errors/408.http
    errorfile 500 /usr/local/etc/haproxy/errors/500.http
    errorfile 502 /usr/local/etc/haproxy/errors/502.http
    errorfile 503 /usr/local/etc/haproxy/errors/503.http
    errorfile 504 /usr/local/etc/haproxy/errors/504.http

frontend websphere_frontend
    bind *:443 ssl crt /usr/local/etc/haproxy/certs/server.pem
    redirect scheme https if !{ ssl_fc }
    default_backend websphere_backend

backend websphere_backend
    balance roundrobin
    option httpchk GET /
    server websphere1 websphere:9080 check
```

### **Step 3: Nginx Configuration**

Create `nginx/Dockerfile`:
```dockerfile
FROM nginx:alpine
COPY nginx.conf /etc/nginx/nginx.conf
COPY html/ /usr/share/nginx/html/
EXPOSE 80
```

Create `nginx/nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    upstream websphere_backend {
        server haproxy:443;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
        }
        
        location /websphere/ {
            proxy_pass https://websphere_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_ssl_verify off;
        }
        
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
```

Create `nginx/html/index.html`:
```html
<!DOCTYPE html>
<html>
<head>
    <title>WebSphere Application Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #0066cc; color: white; padding: 20px; border-radius: 5px; }
        .content { padding: 20px; background: #f5f5f5; margin-top: 20px; border-radius: 5px; }
        .link { display: inline-block; margin: 10px; padding: 10px 20px; background: #0066cc; color: white; text-decoration: none; border-radius: 3px; }
        .link:hover { background: #0052a3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WebSphere Application Server</h1>
            <p>Enterprise Java Application Platform</p>
        </div>
        <div class="content">
            <h2>Welcome to WebSphere</h2>
            <p>Your WebSphere Application Server is running successfully!</p>
            
            <h3>Quick Links:</h3>
            <a href="https://localhost:9043/ibm/console" class="link" target="_blank">Admin Console</a>
            <a href="http://localhost:9080/" class="link" target="_blank">Application Server</a>
            <a href="http://localhost:5500/em/" class="link" target="_blank">Oracle Enterprise Manager</a>
            
            <h3>System Information:</h3>
            <ul>
                <li><strong>WebSphere Version:</strong> Traditional 9.0.5.x</li>
                <li><strong>Oracle Database:</strong> 21c Express Edition</li>
                <li><strong>Java Version:</strong> IBM Java 8</li>
                <li><strong>Container Platform:</strong> Docker</li>
            </ul>
        </div>
    </div>
</body>
</html>
```

### **Step 4: Database Configuration**

Create `database/init/healthcheck.sql`:
```sql
-- Oracle Database Health Check Script
SELECT 'Oracle Database is ready' as status FROM dual;
EXIT;
```

Create `database/init/01_create_sample_tables.sql`:
```sql
-- Sample Database Initialization Script
-- Connect to the pluggable database
ALTER SESSION SET CONTAINER = XEPDB1;

-- Create a sample user for application
CREATE USER savvyspend IDENTIFIED BY SavvySpend123
DEFAULT TABLESPACE USERS
TEMPORARY TABLESPACE TEMP;

-- Grant necessary privileges
GRANT CONNECT, RESOURCE TO savvyspend;
GRANT CREATE SESSION TO savvyspend;
GRANT CREATE TABLE TO savvyspend;
GRANT CREATE SEQUENCE TO savvyspend;
GRANT CREATE VIEW TO savvyspend;
ALTER USER savvyspend QUOTA UNLIMITED ON USERS;

-- Connect as the new user
CONNECT savvyspend/SavvySpend123@XEPDB1;

-- Create sample tables
CREATE TABLE users (
    user_id NUMBER PRIMARY KEY,
    username VARCHAR2(50) NOT NULL UNIQUE,
    email VARCHAR2(100) NOT NULL,
    created_date DATE DEFAULT SYSDATE
);

CREATE TABLE transactions (
    transaction_id NUMBER PRIMARY KEY,
    user_id NUMBER REFERENCES users(user_id),
    amount NUMBER(10,2) NOT NULL,
    description VARCHAR2(200),
    transaction_date DATE DEFAULT SYSDATE
);

-- Create sequences
CREATE SEQUENCE user_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE transaction_seq START WITH 1 INCREMENT BY 1;

-- Insert sample data
INSERT INTO users (user_id, username, email) VALUES (user_seq.NEXTVAL, 'testuser', '<EMAIL>');
INSERT INTO users (user_id, username, email) VALUES (user_seq.NEXTVAL, 'admin', '<EMAIL>');

INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, 1, 100.50, 'Sample transaction 1');
INSERT INTO transactions (transaction_id, user_id, amount, description)
VALUES (transaction_seq.NEXTVAL, 1, -25.00, 'Sample transaction 2');

COMMIT;

-- Create a test view
CREATE VIEW user_transactions AS
SELECT u.username, u.email, t.amount, t.description, t.transaction_date
FROM users u
JOIN transactions t ON u.user_id = t.user_id;

SELECT 'Sample database initialized successfully' as status FROM dual;
```

---

## 🔧 **WebSphere Setup Process**

### **Step 1: Initial Container Startup**
```bash
# Start Oracle database first
docker-compose up -d oracle-db

# Wait for Oracle to be ready (2-3 minutes)
docker-compose logs -f oracle-db

# Start WebSphere
docker-compose up -d websphere

# Monitor WebSphere startup (5-10 minutes)
docker-compose logs -f websphere
```

### **Step 2: Get Admin Password**
```bash
# WebSphere generates a random password on first startup
docker exec websphere-clean cat /tmp/PASSWORD

# Example output: 3I3rdBJT
```

### **Step 3: Access Admin Console**
- **URL**: https://localhost:9043/ibm/console
- **Username**: `wsadmin`
- **Password**: Use the generated password from Step 2
- **Accept SSL certificate warnings** (self-signed certificate)

### **Step 4: Change Admin Password**
1. Login to admin console
2. Navigate to **Users and Groups** → **Manage Users**
3. Click on **wsadmin** user
4. Change password to your custom password (e.g., `Ubi@12345`)
5. **Save** configuration
6. **Restart** WebSphere for changes to take effect

---

## 🔗 **Oracle JDBC Integration**

### **Critical Issue: Oracle JAR Conflicts**

**Problem**: Adding Oracle JDBC JARs can cause authentication failures due to XML parser conflicts.

**Solution**: Use only essential JDBC JARs and avoid conflicting XML parsers.

### **Step 1: Prepare JDBC JARs**
Download from Oracle:
- `ojdbc8.jar` - Oracle 21.3.0 JDBC driver (Java 8 compatible)
- `orai18n.jar` - Oracle internationalization support

**⚠️ IMPORTANT**: Do NOT use these JARs (they cause conflicts):
- `xmlparserv2.jar` - Conflicts with WebSphere XML processing
- `xdb.jar` - Conflicts with WebSphere authentication

### **Step 2: Install JDBC JARs**
```bash
# Copy essential JDBC JARs to WebSphere
docker cp ojdbc8.jar websphere-clean:/opt/IBM/WebSphere/AppServer/lib/
docker cp orai18n.jar websphere-clean:/opt/IBM/WebSphere/AppServer/lib/

# Restart WebSphere to load JARs
docker-compose restart websphere
```

### **Step 3: Verify No Authentication Issues**
```bash
# Check for authentication errors after restart
docker exec websphere-clean tail -50 /opt/IBM/WebSphere/AppServer/profiles/AppSrv01/logs/server1/SystemOut.log | grep -i "SECJ0118E\|authentication.*error"

# Should return no results if successful
```

### **Step 4: Configure Oracle DataSource**

#### **4.1: Create JDBC Provider**
1. **Admin Console** → **Resources** → **JDBC** → **JDBC providers**
2. **Scope**: Server=server1
3. **New** → **Oracle** → **Oracle JDBC Driver** → **Connection pool data source**
4. **Configuration**:
   - **Name**: `Oracle JDBC Provider`
   - **Classpath**: `/opt/IBM/WebSphere/AppServer/lib/ojdbc8.jar`

#### **4.2: Create Authentication Alias**
1. **Security** → **Global security** → **Java Authentication and Authorization Service** → **J2C authentication data**
2. **New**:
   - **Alias**: `OracleAuthAlias`
   - **User ID**: `system`
   - **Password**: `SavvySpend123`

#### **4.3: Create Data Source**
1. **Resources** → **JDBC** → **Data sources**
2. **New**:
   - **Name**: `OracleDataSource`
   - **JNDI**: `jdbc/OracleDS`
   - **Provider**: Oracle JDBC Provider
   - **Helper class**: `com.ibm.websphere.rsadapter.Oracle11gDataStoreHelper`
   - **Authentication alias**: `OracleAuthAlias`

#### **4.4: Configure Custom Properties**
Add these properties to the DataSource:
```
serverName = oracle-db
portNumber = 1521
databaseName = XEPDB1
driverType = 4
URL = ***************************************
```

#### **4.5: Test Connection**
1. Go to **Data sources** → **OracleDataSource**
2. Click **Test connection**
3. **Expected result**: "Test connection was successful"

---

## 🚨 **Troubleshooting Guide**

### **Authentication Issues**

**Symptom**: `SECJ0118E: Authentication error during authentication for user wsadmin`

**Cause**: Oracle XML parser JARs conflicting with WebSphere authentication

**Solution**:
```bash
# Remove conflicting JARs
docker exec websphere-clean rm -f /opt/IBM/WebSphere/AppServer/lib/xmlparserv2.jar
docker exec websphere-clean rm -f /opt/IBM/WebSphere/AppServer/lib/xdb.jar

# Keep only essential JARs
docker exec websphere-clean ls -la /opt/IBM/WebSphere/AppServer/lib/ | grep -E "ojdbc|orai18n"

# Restart WebSphere
docker-compose restart websphere
```

### **Java Version Compatibility**

**Symptom**: `UnsupportedClassVersionError: bad major version 55.0`

**Cause**: JDBC driver compiled with Java 11, but WebSphere runs Java 8

**Solution**: Download Oracle 21.3.0 JDBC driver specifically compiled for Java 8

### **Database Connection Issues**

**Symptom**: Connection test fails

**Troubleshooting**:
```bash
# Check Oracle container status
docker ps | grep oracle

# Check Oracle logs
docker-compose logs oracle-db

# Test Oracle connectivity
docker exec savvyspend-oracle-clean sqlplus system/SavvySpend123@XEPDB1

# Verify network connectivity
docker exec websphere-clean ping oracle-db
```

### **Container Startup Issues**

**WebSphere won't start**:
```bash
# Check container logs
docker-compose logs websphere

# Check available resources
docker system df

# Restart with fresh logs
docker-compose down
docker-compose up -d
```

**Oracle won't start**:
```bash
# Oracle requires significant resources
# Ensure 4GB+ RAM available for Oracle container
# Check disk space (Oracle needs 10GB+)

# Reset Oracle data if corrupted
docker-compose down
docker volume rm websphere-oracle-project_oracle_data_clean
docker-compose up -d oracle-db
```

---

## 📊 **Verification Checklist**

### **✅ WebSphere Verification**
- [ ] Container running: `docker ps | grep websphere`
- [ ] Admin console accessible: https://localhost:9043/ibm/console
- [ ] Custom password working
- [ ] No authentication errors in logs
- [ ] Application server responding: http://localhost:9080/

### **✅ Oracle Database Verification**
- [ ] Container running: `docker ps | grep oracle`
- [ ] Database accessible: `sqlplus system/SavvySpend123@//localhost:1521/XEPDB1`
- [ ] PDB accessible: `sqlplus system/SavvySpend123@//localhost:1521/XEPDB1`
- [ ] Sample data created (if initialization scripts run)

### **✅ JDBC Integration Verification**
- [ ] JDBC JARs present: `docker exec websphere-clean ls /opt/IBM/WebSphere/AppServer/lib/ojdbc8.jar`
- [ ] No conflicting JARs: No xmlparserv2.jar or xdb.jar
- [ ] JDBC Provider created in WebSphere
- [ ] DataSource created and configured
- [ ] Connection test successful

### **✅ Network and Proxy Verification**
- [ ] HAProxy running: `docker ps | grep haproxy`
- [ ] Nginx running: `docker ps | grep nginx`
- [ ] Frontend accessible: http://localhost:80/
- [ ] HTTPS working: https://localhost:443/ (if SSL configured)

---

## 🔧 **Maintenance Commands**

### **Container Management**
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart websphere

# View logs
docker-compose logs -f websphere
docker-compose logs -f oracle-db

# Check container status
docker-compose ps
```

### **Backup and Recovery**
```bash
# Backup WebSphere configuration
docker run --rm -v websphere-oracle-project_websphere_profiles_clean:/data -v $(pwd):/backup alpine tar czf /backup/websphere-backup.tar.gz -C /data .

# Backup Oracle data
docker run --rm -v websphere-oracle-project_oracle_data_clean:/data -v $(pwd):/backup alpine tar czf /backup/oracle-backup.tar.gz -C /data .

# Restore WebSphere configuration
docker run --rm -v websphere-oracle-project_websphere_profiles_clean:/data -v $(pwd):/backup alpine tar xzf /backup/websphere-backup.tar.gz -C /data

# Restore Oracle data
docker run --rm -v websphere-oracle-project_oracle_data_clean:/data -v $(pwd):/backup alpine tar xzf /backup/oracle-backup.tar.gz -C /data
```

### **Performance Monitoring**
```bash
# Monitor resource usage
docker stats

# Check container health
docker-compose ps

# View detailed container info
docker inspect websphere-clean
docker inspect savvyspend-oracle-clean
```

---

## 📈 **Performance Tuning**

### **WebSphere Optimization**
- **JVM Heap Size**: Adjust in WebSphere admin console
- **Connection Pools**: Tune JDBC connection pool settings
- **Thread Pools**: Configure web container thread pools
- **Session Management**: Optimize session timeout and persistence

### **Oracle Database Optimization**
- **Memory Settings**: Configure SGA and PGA sizes
- **Connection Limits**: Adjust maximum connections
- **Tablespace Management**: Monitor and extend tablespaces
- **Index Optimization**: Create appropriate indexes for queries

### **Container Resource Limits**
```yaml
# Add to docker-compose.yml services
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
    reservations:
      memory: 2G
      cpus: '1.0'
```

---

## 🔐 **Security Considerations**

### **WebSphere Security**
- Change default admin password immediately
- Enable SSL/TLS for admin console
- Configure proper user registries (LDAP/Active Directory)
- Implement role-based access control
- Regular security updates

### **Oracle Database Security**
- Change default passwords
- Create application-specific database users
- Implement proper privilege management
- Enable database auditing
- Regular security patches

### **Network Security**
- Use SSL certificates for HTTPS
- Implement proper firewall rules
- Network segmentation for database access
- VPN access for remote administration
- Regular security scanning

---

## 📚 **Additional Resources**

### **Documentation Links**
- [IBM WebSphere Traditional Documentation](https://www.ibm.com/docs/en/was/9.0.5)
- [Oracle Database 21c Documentation](https://docs.oracle.com/en/database/oracle/oracle-database/21/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [HAProxy Configuration Guide](https://www.haproxy.org/download/2.8/doc/configuration.txt)

### **Useful Commands Reference**
```bash
# WebSphere wsadmin commands
/opt/IBM/WebSphere/AppServer/bin/wsadmin.sh -conntype SOAP -host localhost -port 8880 -user wsadmin -password [password]

# Oracle SQL*Plus commands
sqlplus system/SavvySpend123@//localhost:1521/XEPDB1

# Docker debugging
docker exec -it websphere-clean bash
docker exec -it savvyspend-oracle-clean bash
```

### **Log File Locations**
```
# WebSphere logs
/opt/IBM/WebSphere/AppServer/profiles/AppSrv01/logs/server1/SystemOut.log
/opt/IBM/WebSphere/AppServer/profiles/AppSrv01/logs/server1/SystemErr.log

# Oracle logs
/opt/oracle/diag/rdbms/xe/XE/trace/alert_XE.log
```

---

## 🎯 **Project Success Criteria**

### **✅ Completed Objectives**
- [x] WebSphere Application Server containerized and running
- [x] Oracle Database 21c integrated and accessible
- [x] JDBC connectivity established and tested
- [x] Persistent configuration across container restarts
- [x] Custom admin authentication working
- [x] HAProxy and Nginx frontend configured
- [x] Comprehensive documentation created

### **🚀 Ready for Next Phase**
- Application deployment and testing
- Performance optimization
- Production deployment planning
- Monitoring and alerting setup
- Backup and disaster recovery implementation

---

*This guide provides a complete setup for WebSphere Application Server with Oracle Database integration in a containerized environment. Follow the steps carefully and refer to the troubleshooting section for any issues encountered.*
