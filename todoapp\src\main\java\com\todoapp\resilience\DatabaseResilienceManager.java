package com.todoapp.resilience;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.function.Supplier;

/**
 * Database Resilience Manager
 * Provides retry mechanisms, circuit breaker patterns, and connection health monitoring
 * for database operations to improve system resilience
 */
public class DatabaseResilienceManager {
    
    private static final Logger logger = Logger.getLogger(DatabaseResilienceManager.class.getName());
    
    // Retry configuration
    private static final int DEFAULT_MAX_RETRIES = 3;
    private static final long DEFAULT_RETRY_DELAY_MS = 1000; // 1 second
    private static final double RETRY_BACKOFF_MULTIPLIER = 2.0;
    
    // Circuit breaker configuration
    private static final int CIRCUIT_BREAKER_FAILURE_THRESHOLD = 5;
    private static final long CIRCUIT_BREAKER_TIMEOUT_MS = 30000; // 30 seconds
    
    // Connection health monitoring
    private static final long CONNECTION_HEALTH_CHECK_INTERVAL_MS = 60000; // 1 minute
    private static final String HEALTH_CHECK_QUERY = "SELECT 1 FROM DUAL";
    
    private final DataSource dataSource;
    private CircuitBreakerState circuitBreakerState = CircuitBreakerState.CLOSED;
    private int consecutiveFailures = 0;
    private long lastFailureTime = 0;
    private long lastHealthCheck = 0;
    private boolean isHealthy = true;
    
    public DatabaseResilienceManager(DataSource dataSource) {
        this.dataSource = dataSource;
    }
    
    /**
     * Execute database operation with retry and circuit breaker protection
     */
    public <T> T executeWithResilience(DatabaseOperation<T> operation) throws SQLException {
        return executeWithResilience(operation, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_DELAY_MS);
    }
    
    /**
     * Execute database operation with custom retry configuration
     */
    public <T> T executeWithResilience(DatabaseOperation<T> operation, int maxRetries, long retryDelayMs) 
            throws SQLException {
        
        // Check circuit breaker state
        if (circuitBreakerState == CircuitBreakerState.OPEN) {
            if (System.currentTimeMillis() - lastFailureTime > CIRCUIT_BREAKER_TIMEOUT_MS) {
                // Try to half-open the circuit
                circuitBreakerState = CircuitBreakerState.HALF_OPEN;
                logger.info("🔄 Circuit breaker transitioning to HALF_OPEN state");
            } else {
                throw new SQLException("Circuit breaker is OPEN - database operations are temporarily disabled");
            }
        }
        
        SQLException lastException = null;
        long currentRetryDelay = retryDelayMs;
        
        for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                // Perform health check if needed
                performHealthCheckIfNeeded();
                
                // Execute the operation
                T result = operation.execute(dataSource);
                
                // Operation succeeded - reset circuit breaker if needed
                if (circuitBreakerState == CircuitBreakerState.HALF_OPEN) {
                    circuitBreakerState = CircuitBreakerState.CLOSED;
                    consecutiveFailures = 0;
                    logger.info("✅ Circuit breaker reset to CLOSED state");
                }
                
                if (attempt > 1) {
                    logger.info("✅ Database operation succeeded on attempt " + attempt);
                }
                
                return result;
                
            } catch (SQLException e) {
                lastException = e;
                consecutiveFailures++;
                lastFailureTime = System.currentTimeMillis();
                
                logger.log(Level.WARNING, "❌ Database operation failed on attempt " + attempt + ": " + e.getMessage());
                
                // Check if we should open the circuit breaker
                if (consecutiveFailures >= CIRCUIT_BREAKER_FAILURE_THRESHOLD) {
                    circuitBreakerState = CircuitBreakerState.OPEN;
                    logger.log(Level.SEVERE, "🚨 Circuit breaker OPENED due to " + consecutiveFailures + " consecutive failures");
                }
                
                // Don't retry on the last attempt
                if (attempt <= maxRetries) {
                    try {
                        logger.info("⏳ Retrying in " + currentRetryDelay + "ms...");
                        Thread.sleep(currentRetryDelay);
                        currentRetryDelay = (long) (currentRetryDelay * RETRY_BACKOFF_MULTIPLIER);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new SQLException("Database operation interrupted during retry", ie);
                    }
                }
            }
        }
        
        // All retries exhausted
        logger.log(Level.SEVERE, "❌ Database operation failed after " + (maxRetries + 1) + " attempts");
        throw new SQLException("Database operation failed after " + (maxRetries + 1) + " attempts", lastException);
    }
    
    /**
     * Execute database operation with connection management
     */
    public <T> T executeWithConnection(ConnectionOperation<T> operation) throws SQLException {
        return executeWithResilience(dataSource -> {
            try (Connection connection = dataSource.getConnection()) {
                return operation.execute(connection);
            }
        });
    }
    
    /**
     * Execute database operation with transaction management
     */
    public <T> T executeWithTransaction(ConnectionOperation<T> operation) throws SQLException {
        return executeWithConnection(connection -> {
            boolean originalAutoCommit = connection.getAutoCommit();
            try {
                connection.setAutoCommit(false);
                T result = operation.execute(connection);
                connection.commit();
                logger.fine("✅ Transaction committed successfully");
                return result;
            } catch (Exception e) {
                try {
                    connection.rollback();
                    logger.warning("🔄 Transaction rolled back due to error: " + e.getMessage());
                } catch (SQLException rollbackException) {
                    logger.log(Level.SEVERE, "❌ Failed to rollback transaction", rollbackException);
                }
                throw e;
            } finally {
                try {
                    connection.setAutoCommit(originalAutoCommit);
                } catch (SQLException e) {
                    logger.log(Level.WARNING, "Failed to restore auto-commit state", e);
                }
            }
        });
    }
    
    /**
     * Perform database health check if needed
     */
    private void performHealthCheckIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastHealthCheck > CONNECTION_HEALTH_CHECK_INTERVAL_MS) {
            performHealthCheck();
            lastHealthCheck = currentTime;
        }
    }
    
    /**
     * Perform database health check
     */
    public boolean performHealthCheck() {
        try (Connection connection = dataSource.getConnection();
             var statement = connection.prepareStatement(HEALTH_CHECK_QUERY)) {
            
            statement.setQueryTimeout(5); // 5 second timeout
            var resultSet = statement.executeQuery();
            
            if (resultSet.next()) {
                if (!isHealthy) {
                    logger.info("✅ Database health check passed - connection restored");
                }
                isHealthy = true;
                return true;
            }
            
        } catch (SQLException e) {
            if (isHealthy) {
                logger.log(Level.WARNING, "❌ Database health check failed", e);
            }
            isHealthy = false;
        }
        
        return false;
    }
    
    /**
     * Get current circuit breaker state
     */
    public CircuitBreakerState getCircuitBreakerState() {
        return circuitBreakerState;
    }
    
    /**
     * Get consecutive failure count
     */
    public int getConsecutiveFailures() {
        return consecutiveFailures;
    }
    
    /**
     * Check if database is healthy
     */
    public boolean isHealthy() {
        return isHealthy;
    }
    
    /**
     * Reset circuit breaker manually (for administrative purposes)
     */
    public void resetCircuitBreaker() {
        circuitBreakerState = CircuitBreakerState.CLOSED;
        consecutiveFailures = 0;
        logger.info("🔧 Circuit breaker manually reset to CLOSED state");
    }
    
    /**
     * Get resilience status information
     */
    public ResilienceStatus getStatus() {
        return new ResilienceStatus(
            circuitBreakerState,
            consecutiveFailures,
            isHealthy,
            lastFailureTime,
            lastHealthCheck
        );
    }
    
    /**
     * Functional interface for database operations
     */
    @FunctionalInterface
    public interface DatabaseOperation<T> {
        T execute(DataSource dataSource) throws SQLException;
    }
    
    /**
     * Functional interface for connection-based operations
     */
    @FunctionalInterface
    public interface ConnectionOperation<T> {
        T execute(Connection connection) throws SQLException;
    }
    
    /**
     * Circuit breaker states
     */
    public enum CircuitBreakerState {
        CLOSED,    // Normal operation
        OPEN,      // Failing fast
        HALF_OPEN  // Testing if service is back
    }
    
    /**
     * Resilience status information
     */
    public static class ResilienceStatus {
        private final CircuitBreakerState circuitBreakerState;
        private final int consecutiveFailures;
        private final boolean isHealthy;
        private final long lastFailureTime;
        private final long lastHealthCheck;
        
        public ResilienceStatus(CircuitBreakerState circuitBreakerState, int consecutiveFailures, 
                              boolean isHealthy, long lastFailureTime, long lastHealthCheck) {
            this.circuitBreakerState = circuitBreakerState;
            this.consecutiveFailures = consecutiveFailures;
            this.isHealthy = isHealthy;
            this.lastFailureTime = lastFailureTime;
            this.lastHealthCheck = lastHealthCheck;
        }
        
        // Getters
        public CircuitBreakerState getCircuitBreakerState() { return circuitBreakerState; }
        public int getConsecutiveFailures() { return consecutiveFailures; }
        public boolean isHealthy() { return isHealthy; }
        public long getLastFailureTime() { return lastFailureTime; }
        public long getLastHealthCheck() { return lastHealthCheck; }
        
        @Override
        public String toString() {
            return String.format("ResilienceStatus{state=%s, failures=%d, healthy=%s, lastFailure=%d, lastCheck=%d}",
                circuitBreakerState, consecutiveFailures, isHealthy, lastFailureTime, lastHealthCheck);
        }
    }
}
