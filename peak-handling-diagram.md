# Peak Handling Visualization

## Without Message Queue
```
High Traffic Spike:
[User1] → \
[User2] → |
[User3] → |
[User4] → | → [Database] ← Overwhelmed!
[User5] → |
...      → |
[User100] → /
```

## With ActiveMQ Buffer
```
High Traffic Spike:
[User1] → \
[User2] → |
[User3] → |
[User4] → | → [ActiveMQ Queue] → [Consumer] → [Database] ← Steady load
[User5] → |       (buffer)      (controlled rate)
...      → |
[User100] → /
```

The queue depth increases during the spike, but the database receives a consistent, manageable flow of requests.