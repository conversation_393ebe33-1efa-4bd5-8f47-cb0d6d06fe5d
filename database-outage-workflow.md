# Todo Application Workflow During Database Outage

## 1. User Creates a New Todo

- User fills out todo form and clicks "Save"
- Web application receives the request
- Application attempts to save to database but fails
- Instead of showing error, application:
  - Creates a JMS message with todo details
  - Sends message to ActiveMQ queue
  - Returns success response to user with "processing" status
  - User sees: "Todo created successfully! Processing in background..."

## 2. Behind the Scenes During Outage

- Todo data waits safely in ActiveMQ queue
- Message consumer attempts to process messages but fails due to DB outage
- ActiveMQ keeps messages in the queue (persistence)
- No data is lost during the outage

## 3. When Database Recovers

- Message consumer automatically retries processing messages
- Todos are saved to database in the order they were created
- Status updates from "processing" to "active" or "completed"
- System integrity is maintained without manual intervention