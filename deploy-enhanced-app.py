#!/usr/bin/env python
# WebSphere Application Deployment Script
# Undeploys old todo-list-app and deploys enhanced version with ActiveMQ integration

print("=== Deploying Enhanced Todo Application with ActiveMQ Integration ===")

try:
    # Get the application manager
    appManager = AdminControl.queryNames('type=ApplicationManager,*')
    if not appManager:
        print("❌ Error: Could not find ApplicationManager")
        exit(1)
    
    print("✅ Found ApplicationManager: " + str(appManager))
    
    # Check if the application is currently installed
    appName = 'todo-list-app'
    installedApps = AdminApp.list()
    
    if appName in installedApps:
        print("\n🔄 Undeploying existing application: " + appName)
        
        # Stop the application first
        try:
            AdminControl.invoke(appManager, 'stopApplication', appName)
            print("✅ Application stopped successfully")
        except:
            print("⚠️ Application was not running or already stopped")
        
        # Uninstall the application
        AdminApp.uninstall(appName)
        print("✅ Application uninstalled successfully")
        
        # Save configuration
        AdminConfig.save()
        print("✅ Configuration saved")
    else:
        print("ℹ️ No existing application found to undeploy")
    
    # Deploy the new enhanced application
    print("\n📦 Deploying enhanced application...")
    
    # Installation options
    options = [
        '-appname', appName,
        '-contextroot', '/todo-list-app',
        '-MapModulesToServers', [
            ['todo-list-app.war', 'todo-list-app.war,WEB-INF/web.xml', 'WebSphere:cell=DefaultCell01,node=DefaultNode01,server=server1']
        ],
        '-MapWebModToVH', [
            ['todo-list-app.war', 'todo-list-app.war,WEB-INF/web.xml', 'default_host']
        ],
        '-MapResRefToEJB', [
            ['todo-list-app.war', '', 'jdbc/OracleDS', 'javax.sql.DataSource', 'jdbc/OracleDS', '']
        ]
    ]
    
    # Install the application
    AdminApp.install('/tmp/todo-list-app-enhanced.war', options)
    print("✅ Enhanced application installed successfully")
    
    # Save configuration
    AdminConfig.save()
    print("✅ Configuration saved")
    
    # Start the application
    print("\n🚀 Starting enhanced application...")
    try:
        AdminControl.invoke(appManager, 'startApplication', appName)
        print("✅ Enhanced application started successfully")
    except Exception as e:
        print("⚠️ Failed to start application automatically: " + str(e))
        print("ℹ️ You may need to start it manually from the admin console")
    
    print("\n=== Enhanced Application Deployment Summary ===")
    print("✅ Application Name: " + appName)
    print("✅ Context Root: /todo-list-app")
    print("✅ Enhanced Features:")
    print("   • ActiveMQ Integration")
    print("   • Database Resilience Manager")
    print("   • Circuit Breaker Pattern")
    print("   • Resilience Monitoring Dashboard")
    print("   • Asynchronous Message Processing")
    print("")
    print("🔗 Access Points:")
    print("   • Todo App: http://localhost:9080/todo-list-app/")
    print("   • Resilience Dashboard: http://localhost:9080/todo-list-app/resilience/dashboard")
    print("   • ActiveMQ Console: http://localhost:8161 (admin/admin)")
    print("")
    print("📝 Next Steps:")
    print("1. Verify application is running in WebSphere Admin Console")
    print("2. Test todo operations at http://localhost:9080/todo-list-app/todos")
    print("3. Check resilience dashboard functionality")
    print("4. Monitor ActiveMQ queues for message processing")
    
except Exception as e:
    print("❌ Error occurred during deployment: " + str(e))
    import traceback
    traceback.print_exc()
    exit(1)

print("=== Enhanced Application Deployment Complete ===")
