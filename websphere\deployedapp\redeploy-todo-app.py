#!/usr/bin/env python
# Redeploy Todo List Application - Fixed Version

print("=== Redeploying Todo List Application (Fixed Version) ===")

try:
    # Application details
    appName = "TodoListApp"
    warFile = "/tmp/todo-list-app.war"
    contextRoot = "/todo-list-app"
    
    print("Application Name: " + appName)
    print("WAR File: " + warFile)
    print("Context Root: " + contextRoot)
    
    # Check if application already exists and uninstall it
    apps = AdminApp.list()
    if appName in apps:
        print("Application already exists. Uninstalling...")
        AdminApp.uninstall(appName)
        AdminConfig.save()
        print("✅ Application uninstalled")
        
        # Wait a moment for cleanup
        import time
        time.sleep(5)
    
    # Install application with corrected settings
    print("Installing application with fixed configuration...")
    AdminApp.install(warFile, [
        '-appname', appName,
        '-contextroot', contextRoot,
        '-MapWebModToVH', [['.*', '.*', 'default_host']],
        '-MapModulesToServers', [['.*', '.*', 'WebSphere:cell=DefaultCell01,node=DefaultNode01,server=server1']],
        '-usedefaultbindings',
        '-allowDispatchRemoteInclude',
        '-allowServiceRemoteInclude'
    ])
    
    print("✅ Application installed")
    
    # Save configuration
    print("Saving configuration...")
    AdminConfig.save()
    print("✅ Configuration saved")
    
    # Start application
    print("Starting application...")
    appManager = AdminControl.queryNames('type=ApplicationManager,*')
    if appManager:
        AdminControl.invoke(appManager, 'startApplication', appName)
        print("✅ Application started")
    else:
        print("⚠️  Could not start application automatically")
        print("   Please start it manually from the WebSphere Admin Console")
    
    print("\n=== Deployment Complete ===")
    print("Application Name: " + appName)
    print("Context Root: " + contextRoot)
    print("Access URL: http://localhost:9080" + contextRoot + "/todos")
    print("\nTo verify deployment:")
    print("1. Check WebSphere Admin Console")
    print("2. Access the application URL")
    print("3. Check application logs")
    
except Exception as e:
    print("❌ Deployment failed: " + str(e))
    import traceback
    traceback.print_exc()
