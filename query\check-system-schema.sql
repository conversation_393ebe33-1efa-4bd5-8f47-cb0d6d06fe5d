-- Connect to system schema
CONNECT system/SavvySpend123@//localhost:1521/XEPDB1;

-- Check what schemas exist
SELECT DISTINCT owner
FROM all_tables
WHERE owner NOT IN ('SYS', 'SYSTEM', 'CTXSYS', 'DBSNMP', 'OUTLN', 'TSMSYS')
ORDER BY owner;

-- Look for users tables in all schemas
SELECT owner, table_name, num_rows
FROM all_tables 
WHERE table_name = 'USERS'
ORDER BY owner;

-- Check the structure of users table in different schemas
SELECT owner, column_name, data_type, data_length
FROM all_tab_columns 
WHERE table_name = 'USERS'
AND owner NOT IN ('SYS', 'SYSTEM')
ORDER BY owner, column_id;

-- Look for demo user across all schemas
SELECT 'Searching for demo user across all schemas...' AS info FROM dual;

-- Check SAVVYSPEND schema for demo user
SELECT 'SAVVYSPEND schema:' AS schema_name FROM dual;
SELECT COUNT(*) AS demo_user_count 
FROM savvyspend.users 
WHERE username = 'demo';

EXIT;
