-- Comprehensive Data Distribution Analysis
-- Connect as SYSTEM to access all schemas
CONNECT system/SavvySpend123@//localhost:1521/XEPDB1;

SET PAGESIZE 50;
SET LINESIZE 120;

-- ========================================
-- SCHEMA OVERVIEW
-- ========================================
SELECT '=== SCHEMA OVERVIEW ===' AS analysis_section FROM dual;

SELECT DISTINCT owner AS schema_name,
       COUNT(*) AS table_count
FROM all_tables 
WHERE owner IN ('SYSTEM', 'SAVVYSPEND')
GROUP BY owner
ORDER BY owner;

-- ========================================
-- TABLE COMPARISON
-- ========================================
SELECT '=== TABLE COMPARISON ===' AS analysis_section FROM dual;

SELECT owner, table_name, num_rows
FROM all_tables 
WHERE owner IN ('SYSTEM', 'SAVVYSPEND')
ORDER BY table_name, owner;

-- ========================================
-- USERS TABLE STRUCTURE COMPARISON
-- ========================================
SELECT '=== USERS TABLE STRUCTURE COMPARISON ===' AS analysis_section FROM dual;

-- SYSTEM.USERS structure
SELECT 'SYSTEM.USERS Structure:' AS info FROM dual;
SELECT column_name, data_type, data_length, nullable
FROM all_tab_columns 
WHERE table_name = 'USERS' AND owner = 'SYSTEM'
ORDER BY column_id;

-- SAVVYSPEND.USERS structure  
SELECT 'SAVVYSPEND.USERS Structure:' AS info FROM dual;
SELECT column_name, data_type, data_length, nullable
FROM all_tab_columns 
WHERE table_name = 'USERS' AND owner = 'SAVVYSPEND'
ORDER BY column_id;

-- ========================================
-- USER DATA COMPARISON
-- ========================================
SELECT '=== USER DATA COMPARISON ===' AS analysis_section FROM dual;

-- Users in SYSTEM schema
SELECT 'Users in SYSTEM schema:' AS info FROM dual;
SELECT COUNT(*) AS user_count FROM system.users;
SELECT * FROM system.users WHERE ROWNUM <= 10;

-- Users in SAVVYSPEND schema
SELECT 'Users in SAVVYSPEND schema:' AS info FROM dual;
SELECT COUNT(*) AS user_count FROM savvyspend.users;
SELECT * FROM savvyspend.users ORDER BY user_id;

-- ========================================
-- APPLICATION TABLES ANALYSIS
-- ========================================
SELECT '=== APPLICATION TABLES ANALYSIS ===' AS analysis_section FROM dual;

-- Check for application tables in SYSTEM
SELECT 'Application tables in SYSTEM schema:' AS info FROM dual;
SELECT table_name, num_rows
FROM all_tables 
WHERE owner = 'SYSTEM' 
AND table_name IN ('EXPENSES', 'BUDGETS', 'CATEGORIES', 'GOALS')
ORDER BY table_name;

-- Check for application tables in SAVVYSPEND
SELECT 'Application tables in SAVVYSPEND schema:' AS info FROM dual;
SELECT table_name, num_rows
FROM all_tables 
WHERE owner = 'SAVVYSPEND' 
AND table_name IN ('EXPENSES', 'BUDGETS', 'CATEGORIES', 'GOALS', 'TRANSACTIONS')
ORDER BY table_name;

-- ========================================
-- SEQUENCES ANALYSIS
-- ========================================
SELECT '=== SEQUENCES ANALYSIS ===' AS analysis_section FROM dual;

-- SYSTEM sequences
SELECT 'Sequences in SYSTEM schema:' AS info FROM dual;
SELECT sequence_name, last_number
FROM all_sequences 
WHERE sequence_owner = 'SYSTEM'
ORDER BY sequence_name;

-- SAVVYSPEND sequences
SELECT 'Sequences in SAVVYSPEND schema:' AS info FROM dual;
SELECT sequence_name, last_number
FROM all_sequences 
WHERE sequence_owner = 'SAVVYSPEND'
ORDER BY sequence_name;

EXIT;
