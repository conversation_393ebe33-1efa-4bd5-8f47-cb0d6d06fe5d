-- Fix Payment Methods to Match Enum Constants
-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Enable output for debugging
SET SERVEROUTPUT ON;

-- Get demo user ID
VARIABLE demo_user_id NUMBER;
BEGIN
    SELECT id INTO :demo_user_id FROM users WHERE username = 'demo';
    DBMS_OUTPUT.PUT_LINE('Demo user ID: ' || :demo_user_id);
END;
/

-- Show current payment methods
SELECT 'Current payment methods for demo user:' AS info FROM dual;
SELECT DISTINCT payment_method FROM expenses WHERE user_id = :demo_user_id;

-- Update payment methods to match enum constants (without spaces, using underscores)
-- Based on typical enum naming conventions:
-- Cash -> CASH
-- Credit Card -> CREDIT_CARD  
-- Debit Card -> DEBIT_CARD
-- Digital Wallet -> DIGITAL_WALLET

UPDATE expenses 
SET payment_method = 'CASH'
WHERE user_id = :demo_user_id 
AND payment_method = 'Cash';

UPDATE expenses 
SET payment_method = 'CREDIT_CARD'
WHERE user_id = :demo_user_id 
AND payment_method = 'Credit Card';

UPDATE expenses 
SET payment_method = 'DEBIT_CARD'
WHERE user_id = :demo_user_id 
AND payment_method = 'Debit Card';

UPDATE expenses 
SET payment_method = 'DIGITAL_WALLET'
WHERE user_id = :demo_user_id 
AND payment_method = 'Digital Wallet';

-- Verify the changes
SELECT 'Updated payment methods:' AS info FROM dual;
SELECT DISTINCT payment_method FROM expenses WHERE user_id = :demo_user_id;

-- Show count of each payment method
SELECT 'Payment method counts:' AS info FROM dual;
SELECT payment_method, COUNT(*) as count 
FROM expenses 
WHERE user_id = :demo_user_id 
GROUP BY payment_method 
ORDER BY payment_method;

COMMIT;

SELECT 'Payment methods updated to enum format successfully!' AS status FROM dual;

EXIT;
