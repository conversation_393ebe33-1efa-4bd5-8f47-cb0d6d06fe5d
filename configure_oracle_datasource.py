#!/usr/bin/env python
# Configure Oracle DataSource in WebSphere

print("=== Configuring Oracle DataSource in WebSphere ===")

try:
    # Configuration parameters
    datasourceName = "OracleDataSource"
    jndiName = "jdbc/OracleDS"
    jdbcProviderName = "Oracle JDBC Provider"
    
    # Oracle connection details
    serverName = "oracle-db"
    portNumber = "1521"
    databaseName = "XEPDB1"
    user = "savvyspend"
    password = "SavvySpend123"
    
    print("Creating Oracle JDBC Provider...")
    
    # Get the server scope
    server = AdminConfig.getid('/Server:server1/')
    print("Server scope: " + str(server))
    
    # Create JDBC Provider
    jdbcProvider = AdminConfig.create('JDBCProvider', server, 
        [['name', jdbcProviderName],
         ['description', 'Oracle JDBC Provider for Oracle Database'],
         ['implementationClassName', 'oracle.jdbc.pool.OracleConnectionPoolDataSource'],
         ['classpath', '/opt/IBM/WebSphere/AppServer/lib/ojdbc8.jar'],
         ['providerType', 'Oracle JDBC Driver'],
         ['xa', 'false']])
    
    print("✅ JDBC Provider created: " + str(jdbcProvider))
    
    # Create DataSource
    print("Creating Oracle DataSource...")
    
    dataSource = AdminConfig.create('DataSource', jdbcProvider,
        [['name', datasourceName],
         ['description', 'Oracle DataSource for SavvySpend Application'],
         ['jndiName', jndiName],
         ['datasourceHelperClassname', 'com.ibm.websphere.rsadapter.Oracle11gDataStoreHelper'],
         ['statementCacheSize', 10],
         ['authDataAlias', ''],
         ['authMechanismPreference', 'BASIC_PASSWORD']])
    
    print("✅ DataSource created: " + str(dataSource))
    
    # Configure connection properties
    print("Configuring connection properties...")
    
    # Create property set
    propertySet = AdminConfig.create('J2EEResourcePropertySet', dataSource, [])
    
    # Database name property
    AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'databaseName'], ['value', databaseName]])
    
    # Server name property  
    AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'serverName'], ['value', serverName]])
    
    # Port number property
    AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'portNumber'], ['value', portNumber]])
    
    # Driver type property
    AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'driverType'], ['value', '4']])
    
    # URL property (alternative connection method)
    jdbcUrl = "jdbc:oracle:thin:@" + serverName + ":" + portNumber + "/" + databaseName
    AdminConfig.create('J2EEResourceProperty', propertySet,
        [['name', 'URL'], ['value', jdbcUrl]])
    
    print("✅ Connection properties configured")
    print("   Database: " + databaseName)
    print("   Server: " + serverName + ":" + portNumber)
    print("   JDBC URL: " + jdbcUrl)
    
    # Configure authentication
    print("Configuring authentication...")
    
    # Create authentication alias
    security = AdminConfig.getid('/Security:/')
    authDataEntry = AdminConfig.create('JAASAuthData', security,
        [['alias', 'OracleAuthAlias'],
         ['userId', user],
         ['password', password],
         ['description', 'Oracle Database Authentication']])
    
    print("✅ Authentication alias created: OracleAuthAlias")
    
    # Update datasource to use auth alias
    AdminConfig.modify(dataSource, [['authDataAlias', 'OracleAuthAlias']])
    
    print("✅ DataSource updated with authentication alias")
    
    # Configure connection pool
    print("Configuring connection pool...")
    
    connectionPool = AdminConfig.create('ConnectionPool', dataSource,
        [['minConnections', 5],
         ['maxConnections', 20],
         ['connectionTimeout', 30],
         ['reapTime', 180],
         ['unusedTimeout', 300],
         ['agedTimeout', 0],
         ['purgePolicy', 'EntirePool']])
    
    print("✅ Connection pool configured")
    print("   Min connections: 5")
    print("   Max connections: 20")
    print("   Connection timeout: 30 seconds")
    
    # Save configuration
    print("Saving configuration...")
    AdminConfig.save()
    print("✅ Configuration saved successfully")
    
    print("\n=== Oracle DataSource Configuration Complete ===")
    print("DataSource Name: " + datasourceName)
    print("JNDI Name: " + jndiName)
    print("Database: " + databaseName + " on " + serverName + ":" + portNumber)
    print("User: " + user)
    print("Authentication Alias: OracleAuthAlias")
    print("\nTo test the connection:")
    print("1. Restart WebSphere server")
    print("2. Go to Admin Console > Resources > JDBC > Data sources")
    print("3. Click on '" + datasourceName + "'")
    print("4. Click 'Test connection'")
    
except Exception as e:
    print("❌ Error configuring Oracle DataSource: " + str(e))
    import traceback
    traceback.print_exc()
