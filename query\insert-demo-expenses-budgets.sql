-- Insert Demo Expenses and Budgets Data for Demo User
-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Enable output for debugging
SET SERVEROUTPUT ON;
SET ECHO ON;

-- Get demo user ID
VARIABLE demo_user_id NUMBER;
BEGIN
    SELECT id INTO :demo_user_id FROM users WHERE username = 'demo';
    DBMS_OUTPUT.PUT_LINE('Demo user ID: ' || :demo_user_id);
END;
/

-- Clean up existing data for demo user (if any)
DELETE FROM expenses WHERE user_id = :demo_user_id;
DELETE FROM budgets WHERE user_id = :demo_user_id;
COMMIT;

-- =====================================================
-- INSERT SAMPLE EXPENSES FOR DEMO USER
-- =====================================================

-- Recent expenses (last 30 days)
INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    45.50, 'Grocery shopping at Walmart', DATE '2025-06-10', 'Credit Card', 0, 'Weekly groceries',
    1, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    25.75, 'Lunch at Italian restaurant', DATE '2025-06-12', 'Debit Card', 0, 'Business lunch',
    1, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    65.00, 'Gas station fill-up', DATE '2025-06-08', 'Credit Card', 0, 'Weekly fuel',
    2, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    120.99, 'New running shoes', DATE '2025-06-05', 'Credit Card', 0, 'Nike Air Max',
    3, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    15.99, 'Netflix subscription', DATE '2025-06-01', 'Credit Card', 1, 'Monthly streaming',
    4, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    89.50, 'Electricity bill', DATE '2025-06-03', 'Bank Transfer', 1, 'Monthly utility',
    5, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    35.00, 'Doctor visit copay', DATE '2025-06-07', 'Debit Card', 0, 'Annual checkup',
    6, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    29.99, 'Programming book', DATE '2025-06-09', 'Credit Card', 0, 'Learning Java',
    7, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    25.00, 'Haircut', DATE '2025-06-11', 'Cash', 0, 'Monthly grooming',
    10, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    50.00, 'Birthday gift for friend', DATE '2025-06-06', 'Credit Card', 0, 'Gift card',
    11, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

-- Older expenses (previous month)
INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    180.75, 'Grocery shopping', DATE '2025-05-15', 'Credit Card', 0, 'Monthly groceries',
    1, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    75.00, 'Gas expenses', DATE '2025-05-20', 'Credit Card', 0, 'Fuel for car',
    2, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO expenses (
    amount, description, expense_date, payment_method, is_recurring, notes, 
    category_id, user_id, created_at, updated_at
) VALUES (
    95.50, 'Internet bill', DATE '2025-05-01', 'Bank Transfer', 1, 'Monthly internet',
    5, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

-- =====================================================
-- INSERT SAMPLE BUDGETS FOR DEMO USER
-- =====================================================

-- Current month budgets (June 2025)
INSERT INTO budgets (
    budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active,
    category_id, user_id, created_at, updated_at
) VALUES (
    400.00, 'Monthly food and dining budget', DATE '2025-06-01', DATE '2025-06-30', 'MONTHLY', 80.00, 1,
    1, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO budgets (
    budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active,
    category_id, user_id, created_at, updated_at
) VALUES (
    200.00, 'Monthly transportation budget', DATE '2025-06-01', DATE '2025-06-30', 'MONTHLY', 85.00, 1,
    2, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO budgets (
    budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active,
    category_id, user_id, created_at, updated_at
) VALUES (
    300.00, 'Monthly shopping budget', DATE '2025-06-01', DATE '2025-06-30', 'MONTHLY', 75.00, 1,
    3, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO budgets (
    budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active,
    category_id, user_id, created_at, updated_at
) VALUES (
    100.00, 'Monthly entertainment budget', DATE '2025-06-01', DATE '2025-06-30', 'MONTHLY', 90.00, 1,
    4, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO budgets (
    budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active,
    category_id, user_id, created_at, updated_at
) VALUES (
    250.00, 'Monthly utilities budget', DATE '2025-06-01', DATE '2025-06-30', 'MONTHLY', 85.00, 1,
    5, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

INSERT INTO budgets (
    budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active,
    category_id, user_id, created_at, updated_at
) VALUES (
    150.00, 'Monthly healthcare budget', DATE '2025-06-01', DATE '2025-06-30', 'MONTHLY', 70.00, 1,
    6, :demo_user_id, SYSTIMESTAMP, SYSTIMESTAMP
);

-- Commit all changes
COMMIT;

-- Verification queries
SELECT 'Demo expenses and budgets inserted successfully!' AS status FROM dual;

SELECT 'Total expenses for demo user: ' || COUNT(*) AS expense_count 
FROM expenses WHERE user_id = :demo_user_id;

SELECT 'Total budgets for demo user: ' || COUNT(*) AS budget_count 
FROM budgets WHERE user_id = :demo_user_id;

-- Show expense summary by category
SELECT 
    c.name AS category,
    COUNT(e.id) AS expense_count,
    SUM(e.amount) AS total_amount
FROM expenses e
JOIN categories c ON e.category_id = c.id
WHERE e.user_id = :demo_user_id
GROUP BY c.name
ORDER BY total_amount DESC;

-- Show budget summary
SELECT 
    c.name AS category,
    b.budget_amount,
    b.alert_threshold,
    TO_CHAR(b.start_date, 'YYYY-MM-DD') AS start_date,
    TO_CHAR(b.end_date, 'YYYY-MM-DD') AS end_date
FROM budgets b
JOIN categories c ON b.category_id = c.id
WHERE b.user_id = :demo_user_id
ORDER BY b.budget_amount DESC;

EXIT;
