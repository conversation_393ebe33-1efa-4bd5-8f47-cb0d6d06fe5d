<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.todoapp.entity.Todo" %>
<%@ page import="com.todoapp.service.TodoService.TodoStats" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.util.Date" %>
<%
    List<Todo> todos = (List<Todo>) request.getAttribute("todos");
    TodoStats stats = (TodoStats) request.getAttribute("stats");
    String currentFilter = (String) request.getAttribute("currentFilter");
    String error = (String) request.getAttribute("error");
    
    SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy");
    SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo List - Task Management</title>
    
    <!-- Bootstrap 5.3.0 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6.0.0 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .main-container {
            margin-top: 2rem;
            margin-bottom: 2rem;
            min-width: 320px;
        }

        .container {
            max-width: 1200px;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .todo-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .todo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .todo-item {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.3s ease;
        }

        .todo-item:last-child {
            border-bottom: none;
        }

        .todo-item:hover {
            background-color: #f8f9fa;
        }

        .todo-completed {
            opacity: 0.7;
            text-decoration: line-through;
        }

        .priority-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 20px;
        }

        .btn-action {
            border: none;
            background: none;
            color: #6c757d;
            font-size: 1.1rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            background-color: #e9ecef;
            color: var(--primary-color);
        }

        .btn-toggle:hover {
            color: var(--success-color);
        }

        .btn-edit:hover {
            color: var(--warning-color);
        }

        .btn-delete:hover {
            color: var(--danger-color);
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .filter-tabs .nav-link {
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.25rem;
            color: var(--primary-color);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-tabs .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background-color: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(90deg, var(--success-color), #2ecc71);
        }

        .due-date {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .overdue {
            color: var(--danger-color);
            font-weight: 600;
        }

        /* Desktop-first approach */
        @media (min-width: 992px) {
            .container {
                max-width: 1140px;
            }

            .todo-item {
                padding: 1.5rem;
            }

            .stats-card {
                margin-bottom: 0;
            }
        }

        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 1rem;
            }

            .todo-item {
                padding: 1rem;
            }

            .btn-action {
                font-size: 1rem;
                padding: 0.25rem;
            }

            .container {
                max-width: 100%;
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/todo-list-app/todos">
                <i class="fas fa-tasks me-2"></i>Todo List
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#addTodoModal">
                            <i class="fas fa-plus me-1"></i>Add Todo
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <div class="row">
            <!-- Sidebar: Statistics Cards -->
            <div class="col-lg-3 col-md-4 mb-4">
                <div class="card stats-card mb-4">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-primary me-3">
                            <i class="fas fa-list"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><%= stats != null ? stats.getTotal() : 0 %></h5>
                            <p class="card-text text-muted mb-0">Total Tasks</p>
                        </div>
                    </div>
                </div>
                <div class="card stats-card mb-4">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-success me-3">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><%= stats != null ? stats.getCompleted() : 0 %></h5>
                            <p class="card-text text-muted mb-0">Completed</p>
                        </div>
                    </div>
                </div>
                <div class="card stats-card mb-4">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-warning me-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><%= stats != null ? stats.getPending() : 0 %></h5>
                            <p class="card-text text-muted mb-0">Pending</p>
                        </div>
                    </div>
                </div>
                <div class="card stats-card mb-4">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-danger me-3">
                            <i class="fas fa-exclamation"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><%= stats != null ? stats.getOverdue() : 0 %></h5>
                            <p class="card-text text-muted mb-0">Overdue</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Content: Todo List -->
            <div class="col-lg-9 col-md-8">
                <!-- Error Alert -->
                <% if (error != null) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><%= error %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <% } %>
                <!-- Progress Bar -->
                <% if (stats != null && stats.getTotal() > 0) { %>
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Overall Progress</h6>
                            <span class="text-muted"><%= String.format("%.1f", stats.getCompletionPercentage()) %>%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                 style="width: <%= stats.getCompletionPercentage() %>%;"></div>
                        </div>
                    </div>
                </div>
                <% } %>
                <!-- Filter Tabs -->
                <div class="filter-tabs">
                    <ul class="nav nav-pills justify-content-center">
                        <li class="nav-item">
                            <a class="nav-link <%= "all".equals(currentFilter) ? "active" : "" %>"
                               href="/todo-list-app/todos?filter=all">
                                <i class="fas fa-list me-1"></i>All Tasks
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= "pending".equals(currentFilter) ? "active" : "" %>"
                               href="/todo-list-app/todos?filter=pending">
                                <i class="fas fa-clock me-1"></i>Pending
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= "completed".equals(currentFilter) ? "active" : "" %>"
                               href="/todo-list-app/todos?filter=completed">
                                <i class="fas fa-check me-1"></i>Completed
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- Todo List -->
                <div class="card todo-card">
                    <% if (todos != null && !todos.isEmpty()) { %>
                        <% for (Todo todo : todos) { %>
                        <div class="todo-item <%= todo.isCompleted() ? "todo-completed" : "" %>"
                             data-todo-id="<%= todo.getTodoId() %>">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-start">
                                        <button class="btn btn-action btn-toggle me-3"
                                                onclick="toggleTodo('<%= todo.getTodoId() %>')"
                                                title="<%= todo.isCompleted() ? "Mark as pending" : "Mark as completed" %>">
                                            <i class="fas fa-<%= todo.isCompleted() ? "undo" : "check" %>"></i>
                                        </button>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 <%= todo.isCompleted() ? "text-muted" : "" %>">
                                                <%= todo.getTitle() %>
                                            </h6>
                                            <% if (todo.getDescription() != null && !todo.getDescription().trim().isEmpty()) { %>
                                            <p class="mb-2 text-muted small"><%= todo.getDescription() %></p>
                                            <% } %>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge priority-badge <%= todo.getPriorityBadgeClass() %>">
                                                    <%= todo.getPriority() %>
                                                </span>
                                                <span class="badge <%= todo.getStatusBadgeClass() %>">
                                                    <%= todo.getStatusText() %>
                                                </span>
                                                <% if (todo.getDueDate() != null) { %>
                                                <span class="due-date <%= todo.isOverdue() ? "overdue" : "" %>">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Due: <%= dateFormat.format(todo.getDueDate()) %>
                                                    <% if (todo.isOverdue()) { %>
                                                    <i class="fas fa-exclamation-triangle ms-1"></i>
                                                    <% } %>
                                                </span>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group">
                                        <button class="btn btn-action btn-edit"
                                                onclick="editTodo('<%= todo.getTodoId() %>')"
                                                title="Edit todo">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-action btn-delete"
                                                onclick="deleteTodo('<%= todo.getTodoId() %>')"
                                                title="Delete todo">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <div class="text-muted small mt-2">
                                        Created: <%= dateFormat.format(todo.getCreatedDate()) %>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <% } %>
                    <% } else { %>
                        <div class="empty-state">
                            <i class="fas fa-tasks"></i>
                            <h5>No todos found</h5>
                            <p class="mb-3">
                                <% if ("completed".equals(currentFilter)) { %>
                                    You haven't completed any tasks yet.
                                <% } else if ("pending".equals(currentFilter)) { %>
                                    You don't have any pending tasks.
                                <% } else { %>
                                    Start organizing your tasks by creating your first todo.
                                <% } %>
                            </p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTodoModal">
                                <i class="fas fa-plus me-1"></i>Add Your First Todo
                            </button>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Todo Modal -->
    <div class="modal fade" id="addTodoModal" tabindex="-1" aria-labelledby="addTodoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTodoModalLabel">
                        <i class="fas fa-plus me-2"></i>Add New Todo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addTodoForm">
                        <div class="mb-3">
                            <label for="addTitle" class="form-label">Title *</label>
                            <input type="text" class="form-control" id="addTitle" name="title" required
                                   placeholder="Enter todo title">
                        </div>
                        <div class="mb-3">
                            <label for="addDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="addDescription" name="description" rows="3"
                                      placeholder="Enter todo description (optional)"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="addPriority" class="form-label">Priority</label>
                                <select class="form-select" id="addPriority" name="priority">
                                    <option value="LOW">Low</option>
                                    <option value="MEDIUM" selected>Medium</option>
                                    <option value="HIGH">High</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="addDueDate" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="addDueDate" name="dueDate">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveTodo()">
                        <i class="fas fa-save me-1"></i>Save Todo
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Todo Modal -->
    <div class="modal fade" id="editTodoModal" tabindex="-1" aria-labelledby="editTodoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTodoModalLabel">
                        <i class="fas fa-edit me-2"></i>Edit Todo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editTodoForm">
                        <input type="hidden" id="editTodoId" name="todoId">
                        <div class="mb-3">
                            <label for="editTitle" class="form-label">Title *</label>
                            <input type="text" class="form-control" id="editTitle" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="editPriority" class="form-label">Priority</label>
                                <select class="form-select" id="editPriority" name="priority">
                                    <option value="LOW">Low</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="HIGH">High</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="editDueDate" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="editDueDate" name="dueDate">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="editCompleted" class="form-label">Status</label>
                                <select class="form-select" id="editCompleted" name="completed">
                                    <option value="false">Pending</option>
                                    <option value="true">Completed</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateTodo()">
                        <i class="fas fa-save me-1"></i>Update Todo
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.0 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentEditTodoId = null;

        // Show toast notification
        function showToast(message, type) {
            if (!type) type = 'success';
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();
            const toastId = 'toast-' + Date.now();
            const bgClass = (type === 'success') ? 'bg-success' : 'bg-danger';
            const iconClass = (type === 'success') ? 'check' : 'exclamation-triangle';

            const toastHtml = '<div id="' + toastId + '" class="toast align-items-center text-white ' + bgClass + ' border-0" role="alert">' +
                '<div class="d-flex">' +
                    '<div class="toast-body">' +
                        '<i class="fas fa-' + iconClass + ' me-2"></i>' +
                        message +
                    '</div>' +
                    '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                '</div>' +
            '</div>';

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // Create toast container if it doesn't exist
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }

        // Save new todo
        function saveTodo() {
            const form = document.getElementById('addTodoForm');
            const formData = new FormData(form);
            formData.append('action', 'create');

            // Convert FormData to URLSearchParams for better compatibility
            const params = new URLSearchParams();
            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }

            // Debug: Log form data
            console.log('Form data being sent:');
            for (let [key, value] of params.entries()) {
                console.log(key + ': ' + value);
            }

            fetch('/todo-list-app/todos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: params.toString()
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    showToast('Todo created successfully!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addTodoModal'));
                    modal.hide();
                    form.reset();
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showToast(data.message || 'Failed to create todo', 'error');
                }
            })
            .catch(function(error) {
                console.error('Error:', error);
                showToast('An error occurred while creating the todo', 'error');
            });
        }

        // Toggle todo completion status
        function toggleTodo(todoId) {
            const params = new URLSearchParams();
            params.append('action', 'toggle');
            params.append('todoId', todoId);

            fetch('/todo-list-app/todos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: params.toString()
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    showToast('Todo status updated!');
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showToast(data.message || 'Failed to update todo status', 'error');
                }
            })
            .catch(function(error) {
                console.error('Error:', error);
                showToast('An error occurred while updating the todo', 'error');
            });
        }

        // Delete todo
        function deleteTodo(todoId) {
            if (!confirm('Are you sure you want to delete this todo?')) {
                return;
            }

            const params = new URLSearchParams();
            params.append('action', 'delete');
            params.append('todoId', todoId);

            fetch('/todo-list-app/todos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: params.toString()
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    showToast('Todo deleted successfully!');
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showToast(data.message || 'Failed to delete todo', 'error');
                }
            })
            .catch(function(error) {
                console.error('Error:', error);
                showToast('An error occurred while deleting the todo', 'error');
            });
        }

        // Edit todo - populate modal with current data
        function editTodo(todoId) {
            // Find the todo item in the DOM to get current values
            const todoItem = document.querySelector('[data-todo-id="' + todoId + '"]');
            if (!todoItem) {
                showToast('Todo not found', 'error');
                return;
            }

            // Extract current values from the DOM
            const title = todoItem.querySelector('h6').textContent.trim();
            const descriptionElement = todoItem.querySelector('p.text-muted.small');
            const description = descriptionElement ? descriptionElement.textContent.trim() : '';
            const priorityBadge = todoItem.querySelector('.priority-badge');
            const priority = priorityBadge ? priorityBadge.textContent.trim() : 'MEDIUM';
            const isCompleted = todoItem.classList.contains('todo-completed');

            // Populate edit form
            document.getElementById('editTodoId').value = todoId;
            document.getElementById('editTitle').value = title;
            document.getElementById('editDescription').value = description;
            document.getElementById('editPriority').value = priority;
            document.getElementById('editCompleted').value = isCompleted.toString();

            // Extract due date if present
            const dueDateElement = todoItem.querySelector('.due-date');
            if (dueDateElement) {
                const dueDateText = dueDateElement.textContent;
                const dateMatch = dueDateText.match(/Due: (.+?)(?:\s|$)/);
                if (dateMatch) {
                    try {
                        const dueDate = new Date(dateMatch[1]);
                        const formattedDate = dueDate.toISOString().split('T')[0];
                        document.getElementById('editDueDate').value = formattedDate;
                    } catch (e) {
                        console.warn('Could not parse due date:', dateMatch[1]);
                    }
                }
            } else {
                document.getElementById('editDueDate').value = '';
            }

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editTodoModal'));
            modal.show();
        }

        // Update todo
        function updateTodo() {
            const form = document.getElementById('editTodoForm');
            const formData = new FormData(form);
            formData.append('action', 'update');

            // Convert FormData to URLSearchParams for better compatibility
            const params = new URLSearchParams();
            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }

            fetch('/todo-list-app/todos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: params.toString()
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    showToast('Todo updated successfully!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editTodoModal'));
                    modal.hide();
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showToast(data.message || 'Failed to update todo', 'error');
                }
            })
            .catch(function(error) {
                console.error('Error:', error);
                showToast('An error occurred while updating the todo', 'error');
            });
        }

        // Set minimum date to today for due date inputs
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const addDueDate = document.getElementById('addDueDate');
            const editDueDate = document.getElementById('editDueDate');
            if (addDueDate) addDueDate.setAttribute('min', today);
            if (editDueDate) editDueDate.setAttribute('min', today);
        });
    </script>
</body>
</html>
