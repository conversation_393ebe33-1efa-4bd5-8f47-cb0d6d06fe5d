-- Verify Payment Methods are in Correct Enum Format
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

SELECT 'All payment methods in database:' AS info FROM dual;
SELECT DISTINCT payment_method FROM expenses ORDER BY payment_method;

SELECT 'Demo user payment method summary:' AS info FROM dual;
SELECT payment_method, COUNT(*) as count 
FROM expenses 
WHERE user_id = 2
GROUP BY payment_method 
ORDER BY payment_method;

SELECT 'Recent demo user expenses:' AS info FROM dual;
SELECT 
    id,
    amount,
    payment_method,
    expense_date
FROM expenses 
WHERE user_id = 2
ORDER BY expense_date DESC;

EXIT;
