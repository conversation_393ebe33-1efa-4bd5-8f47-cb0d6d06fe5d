package com.todoapp.messaging;

import com.todoapp.entity.Todo;
import com.todoapp.service.TodoService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import javax.annotation.Resource;
import javax.jms.*;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Message Consumer for Todo Operations
 * Processes todo operations from ActiveMQ queues with database resilience
 */
public class TodoMessageConsumer implements MessageListener {
    
    private static final Logger logger = Logger.getLogger(TodoMessageConsumer.class.getName());
    
    @Resource(mappedName = "jms/TodoConnectionFactory")
    private ConnectionFactory connectionFactory;
    
    private TodoService todoService;
    private ObjectMapper objectMapper;
    private AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // Retry configuration
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 5000; // 5 seconds
    
    public TodoMessageConsumer() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.todoService = new TodoService();
    }
    
    /**
     * Start consuming messages from all todo queues
     */
    public void startConsuming() {
        if (isRunning.compareAndSet(false, true)) {
            logger.info("🚀 Starting Todo Message Consumer...");
            
            try {
                // Start consumers for each queue
                startQueueConsumer("todo.create.queue");
                startQueueConsumer("todo.update.queue");
                startQueueConsumer("todo.delete.queue");
                startQueueConsumer("todo.toggle.queue");
                startQueueConsumer("todo.audit.queue");
                
                logger.info("✅ Todo Message Consumer started successfully");
                
            } catch (Exception e) {
                logger.log(Level.SEVERE, "❌ Failed to start Todo Message Consumer", e);
                isRunning.set(false);
                throw new RuntimeException("Failed to start message consumer", e);
            }
        } else {
            logger.warning("Todo Message Consumer is already running");
        }
    }
    
    /**
     * Stop consuming messages
     */
    public void stopConsuming() {
        if (isRunning.compareAndSet(true, false)) {
            logger.info("🛑 Stopping Todo Message Consumer...");
            // Additional cleanup logic can be added here
            logger.info("✅ Todo Message Consumer stopped");
        }
    }
    
    /**
     * Start consumer for a specific queue
     */
    private void startQueueConsumer(String queueName) throws JMSException, NamingException {
        
        // Initialize JMS resources
        if (connectionFactory == null) {
            Context ctx = new InitialContext();
            connectionFactory = (ConnectionFactory) ctx.lookup("jms/TodoConnectionFactory");
        }
        
        // Create connection and session
        Connection connection = connectionFactory.createConnection();
        Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
        
        // Lookup queue
        Context ctx = new InitialContext();
        Queue queue = (Queue) ctx.lookup("jms/" + queueName);
        
        // Create consumer
        MessageConsumer consumer = session.createConsumer(queue);
        consumer.setMessageListener(this);
        
        // Start connection
        connection.start();
        
        logger.info("✅ Started consumer for queue: " + queueName);
    }
    
    /**
     * Process incoming JMS messages
     */
    @Override
    public void onMessage(Message message) {
        try {
            if (message instanceof TextMessage) {
                TextMessage textMessage = (TextMessage) message;
                String messageBody = textMessage.getText();
                String messageType = textMessage.getStringProperty("MessageType");
                
                logger.info("📨 Received message type: " + messageType);
                
                // Parse JSON message
                JsonNode messageData = objectMapper.readTree(messageBody);
                String operation = messageData.get("operation").asText();
                
                // Process message with retry logic
                processMessageWithRetry(operation, messageData, 1);
                
            } else {
                logger.warning("⚠️ Received non-text message, ignoring");
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Error processing message", e);
            // Message will be redelivered or sent to DLQ based on broker configuration
        }
    }
    
    /**
     * Process message with retry logic for database resilience
     */
    private void processMessageWithRetry(String operation, JsonNode messageData, int attempt) {
        try {
            
            switch (operation.toUpperCase()) {
                case "CREATE":
                    processCreateTodo(messageData);
                    break;
                case "UPDATE":
                    processUpdateTodo(messageData);
                    break;
                case "DELETE":
                    processDeleteTodo(messageData);
                    break;
                case "TOGGLE":
                    processToggleTodo(messageData);
                    break;
                case "AUDIT":
                    processAuditMessage(messageData);
                    break;
                default:
                    logger.warning("⚠️ Unknown operation: " + operation);
                    return;
            }
            
            logger.info("✅ Successfully processed " + operation + " operation");
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "❌ Failed to process " + operation + " operation (attempt " + attempt + ")", e);
            
            if (attempt < MAX_RETRY_ATTEMPTS) {
                // Retry with exponential backoff
                try {
                    long delay = RETRY_DELAY_MS * attempt;
                    logger.info("⏳ Retrying in " + delay + "ms...");
                    Thread.sleep(delay);
                    processMessageWithRetry(operation, messageData, attempt + 1);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    logger.log(Level.SEVERE, "Retry interrupted", ie);
                }
            } else {
                logger.log(Level.SEVERE, "❌ Max retry attempts reached for " + operation + " operation", e);
                // Message will be sent to Dead Letter Queue
                throw new RuntimeException("Max retry attempts reached", e);
            }
        }
    }
    
    /**
     * Process create todo message
     */
    private void processCreateTodo(JsonNode messageData) {
        try {
            JsonNode todoNode = messageData.get("todo");
            String userId = messageData.get("userId").asText();
            
            // Convert JSON to Todo object
            Todo todo = objectMapper.treeToValue(todoNode, Todo.class);
            
            // Create todo in database
            Todo createdTodo = todoService.createTodo(todo);
            
            if (createdTodo != null) {
                logger.info("✅ Created todo: " + createdTodo.getTitle() + " (ID: " + createdTodo.getTodoId() + ")");
            } else {
                throw new RuntimeException("Failed to create todo in database");
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to process create todo message", e);
            throw new RuntimeException("Create todo processing failed", e);
        }
    }
    
    /**
     * Process update todo message
     */
    private void processUpdateTodo(JsonNode messageData) {
        try {
            JsonNode todoNode = messageData.get("todo");
            String userId = messageData.get("userId").asText();
            
            // Convert JSON to Todo object
            Todo todo = objectMapper.treeToValue(todoNode, Todo.class);
            
            // Update todo in database
            Todo updatedTodo = todoService.updateTodo(todo);
            
            if (updatedTodo != null) {
                logger.info("✅ Updated todo: " + updatedTodo.getTitle() + " (ID: " + updatedTodo.getTodoId() + ")");
            } else {
                throw new RuntimeException("Failed to update todo in database");
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to process update todo message", e);
            throw new RuntimeException("Update todo processing failed", e);
        }
    }
    
    /**
     * Process delete todo message
     */
    private void processDeleteTodo(JsonNode messageData) {
        try {
            Long todoId = messageData.get("todoId").asLong();
            String userId = messageData.get("userId").asText();
            
            // Delete todo from database
            boolean deleted = todoService.deleteTodo(todoId);
            
            if (deleted) {
                logger.info("✅ Deleted todo with ID: " + todoId);
            } else {
                throw new RuntimeException("Failed to delete todo from database");
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to process delete todo message", e);
            throw new RuntimeException("Delete todo processing failed", e);
        }
    }
    
    /**
     * Process toggle todo status message
     */
    private void processToggleTodo(JsonNode messageData) {
        try {
            Long todoId = messageData.get("todoId").asLong();
            String userId = messageData.get("userId").asText();
            
            // Toggle todo status in database
            boolean toggled = todoService.toggleTodoStatus(todoId);
            
            if (toggled) {
                logger.info("✅ Toggled status for todo ID: " + todoId);
            } else {
                throw new RuntimeException("Failed to toggle todo status in database");
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to process toggle todo message", e);
            throw new RuntimeException("Toggle todo processing failed", e);
        }
    }
    
    /**
     * Process audit message
     */
    private void processAuditMessage(JsonNode messageData) {
        try {
            String operation = messageData.get("operation").asText();
            Long todoId = messageData.get("todoId").asLong();
            String userId = messageData.get("userId").asText();
            long timestamp = messageData.get("timestamp").asLong();
            
            // Log audit information
            logger.info("📋 AUDIT: User " + userId + " performed " + operation + " on todo " + todoId + " at " + timestamp);
            
            // Here you could store audit information in a separate audit table
            // For now, we'll just log it
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to process audit message", e);
            // Don't throw exception for audit messages - they're not critical
        }
    }
    
    /**
     * Check if consumer is running
     */
    public boolean isRunning() {
        return isRunning.get();
    }
}
