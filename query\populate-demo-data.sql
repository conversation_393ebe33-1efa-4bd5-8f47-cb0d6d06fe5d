-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Get the demo user ID
VARIABLE demo_user_id NUMBER;
BEGIN
  SELECT id INTO :demo_user_id FROM users WHERE username = 'demo';
  DBMS_OUTPUT.PUT_LINE('Demo user ID: ' || :demo_user_id);
EXCEPTION
  WHEN NO_DATA_FOUND THEN
    DBMS_OUTPUT.PUT_LINE('Demo user not found. Please run the application first to create the demo user.');
    :demo_user_id := NULL;
END;
/

-- Only proceed if demo user exists
BEGIN
  IF :demo_user_id IS NOT NULL THEN
    -- Clear existing data for demo user (if any)
    DELETE FROM expenses WHERE user_id = :demo_user_id;
    DELETE FROM budgets WHERE user_id = :demo_user_id;
    DELETE FROM goals WHERE user_id = :demo_user_id;
    
    -- Get category IDs
    DECLARE
      v_food_id NUMBER;
      v_transport_id NUMBER;
      v_shopping_id NUMBER;
      v_entertainment_id NUMBER;
      v_bills_id NUMBER;
      v_health_id NUMBER;
      v_education_id NUMBER;
      v_travel_id NUMBER;
      
      -- Current date for reference
      v_current_date DATE := SYSDATE;
      v_last_month DATE := ADD_MONTHS(SYSDATE, -1);
      v_two_months_ago DATE := ADD_MONTHS(SYSDATE, -2);
    BEGIN
      -- Get category IDs
      SELECT id INTO v_food_id FROM categories WHERE name = 'Food & Dining' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      SELECT id INTO v_transport_id FROM categories WHERE name = 'Transportation' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      SELECT id INTO v_shopping_id FROM categories WHERE name = 'Shopping' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      SELECT id INTO v_entertainment_id FROM categories WHERE name = 'Entertainment' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      SELECT id INTO v_bills_id FROM categories WHERE name = 'Bills & Utilities' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      
      -- Try to get other categories, but don't fail if they don't exist
      BEGIN
        SELECT id INTO v_health_id FROM categories WHERE name = 'Health & Medical' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN v_health_id := v_shopping_id; -- Fallback
      END;
      
      BEGIN
        SELECT id INTO v_education_id FROM categories WHERE name = 'Education' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN v_education_id := v_shopping_id; -- Fallback
      END;
      
      BEGIN
        SELECT id INTO v_travel_id FROM categories WHERE name = 'Travel' AND (user_id IS NULL OR user_id = :demo_user_id) AND ROWNUM = 1;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN v_travel_id := v_entertainment_id; -- Fallback
      END;
      
      -- Insert expenses for current month
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (45.75, 'Grocery shopping at Whole Foods', v_current_date - 2, 'CREDIT_CARD', v_food_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (28.50, 'Gas station fill-up', v_current_date - 3, 'DEBIT_CARD', v_transport_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (89.99, 'New shoes from Nike', v_current_date - 5, 'CREDIT_CARD', v_shopping_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (12.99, 'Netflix subscription', v_current_date - 7, 'CREDIT_CARD', v_entertainment_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (125.40, 'Electricity bill', v_current_date - 8, 'BANK_TRANSFER', v_bills_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (35.00, 'Pharmacy - Prescription', v_current_date - 10, 'DEBIT_CARD', v_health_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (22.50, 'Lunch with colleagues', v_current_date - 1, 'CASH', v_food_id, :demo_user_id, v_current_date, v_current_date);
      
      -- Insert expenses for last month
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (42.30, 'Grocery shopping at Safeway', v_last_month + 15, 'CREDIT_CARD', v_food_id, :demo_user_id, v_last_month + 15, v_last_month + 15);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (32.10, 'Gas station fill-up', v_last_month + 10, 'DEBIT_CARD', v_transport_id, :demo_user_id, v_last_month + 10, v_last_month + 10);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (120.50, 'Winter jacket', v_last_month + 5, 'CREDIT_CARD', v_shopping_id, :demo_user_id, v_last_month + 5, v_last_month + 5);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (15.99, 'Movie tickets', v_last_month + 20, 'CREDIT_CARD', v_entertainment_id, :demo_user_id, v_last_month + 20, v_last_month + 20);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (85.20, 'Internet bill', v_last_month + 25, 'BANK_TRANSFER', v_bills_id, :demo_user_id, v_last_month + 25, v_last_month + 25);
      
      -- Insert expenses for two months ago
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (38.45, 'Grocery shopping at Trader Joe''s', v_two_months_ago + 5, 'CREDIT_CARD', v_food_id, :demo_user_id, v_two_months_ago + 5, v_two_months_ago + 5);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (30.00, 'Gas station fill-up', v_two_months_ago + 12, 'DEBIT_CARD', v_transport_id, :demo_user_id, v_two_months_ago + 12, v_two_months_ago + 12);
      
      INSERT INTO expenses (amount, description, date, payment_method, category_id, user_id, created_at, updated_at)
      VALUES (65.75, 'Books from Amazon', v_two_months_ago + 18, 'CREDIT_CARD', v_education_id, :demo_user_id, v_two_months_ago + 18, v_two_months_ago + 18);
      
      -- Create budgets
      INSERT INTO budgets (name, amount, start_date, end_date, category_id, user_id, created_at, updated_at)
      VALUES ('Monthly Groceries', 300.00, TRUNC(v_current_date, 'MM'), LAST_DAY(v_current_date), v_food_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO budgets (name, amount, start_date, end_date, category_id, user_id, created_at, updated_at)
      VALUES ('Transportation Budget', 150.00, TRUNC(v_current_date, 'MM'), LAST_DAY(v_current_date), v_transport_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO budgets (name, amount, start_date, end_date, category_id, user_id, created_at, updated_at)
      VALUES ('Entertainment', 100.00, TRUNC(v_current_date, 'MM'), LAST_DAY(v_current_date), v_entertainment_id, :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO budgets (name, amount, start_date, end_date, category_id, user_id, created_at, updated_at)
      VALUES ('Shopping Limit', 200.00, TRUNC(v_current_date, 'MM'), LAST_DAY(v_current_date), v_shopping_id, :demo_user_id, v_current_date, v_current_date);
      
      -- Create financial goals
      INSERT INTO goals (name, target_amount, current_amount, deadline, description, status, user_id, created_at, updated_at)
      VALUES ('Vacation Fund', 2000.00, 750.00, ADD_MONTHS(v_current_date, 6), 'Saving for summer vacation to Hawaii', 'IN_PROGRESS', :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO goals (name, target_amount, current_amount, deadline, description, status, user_id, created_at, updated_at)
      VALUES ('Emergency Fund', 5000.00, 1200.00, ADD_MONTHS(v_current_date, 12), '3 months of living expenses', 'IN_PROGRESS', :demo_user_id, v_current_date, v_current_date);
      
      INSERT INTO goals (name, target_amount, current_amount, deadline, description, status, user_id, created_at, updated_at)
      VALUES ('New Laptop', 1200.00, 1200.00, v_last_month + 15, 'MacBook Air for work', 'COMPLETED', :demo_user_id, v_two_months_ago + 10, v_last_month + 15);
      
      COMMIT;
      DBMS_OUTPUT.PUT_LINE('Demo data successfully populated for user demo (ID: ' || :demo_user_id || ')');
    END;
  END IF;
END;
/

-- Verify data was inserted
SELECT 'Expenses' AS data_type, COUNT(*) AS record_count FROM expenses WHERE user_id = :demo_user_id
UNION ALL
SELECT 'Budgets' AS data_type, COUNT(*) AS record_count FROM budgets WHERE user_id = :demo_user_id
UNION ALL
SELECT 'Goals' AS data_type, COUNT(*) AS record_count FROM goals WHERE user_id = :demo_user_id;

-- Show expense summary by category
SELECT c.name AS category, COUNT(e.id) AS expense_count, SUM(e.amount) AS total_spent
FROM expenses e
JOIN categories c ON e.category_id = c.id
WHERE e.user_id = :demo_user_id
GROUP BY c.name
ORDER BY total_spent DESC;

EXIT;