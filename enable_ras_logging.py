#!/usr/bin/env python
# Enable RAS Logging Service in WebSphere to populate SystemOut.log and SystemErr.log

print("=== Enabling RAS Logging Service in WebSphere ===")

try:
    # Get the server scope
    server = AdminConfig.getid('/Server:server1/')
    print("Server scope: " + str(server))
    
    if not server:
        print("❌ Error: Could not find server1")
        exit(1)
    
    # Get the RAS Logging Service
    rasLoggingService = AdminConfig.list('RASLoggingService', server)
    print("RAS Logging Service: " + str(rasLoggingService))
    
    if rasLoggingService:
        # Enable the RAS Logging Service
        print("Enabling RAS Logging Service...")
        AdminConfig.modify(rasLoggingService, [['enable', 'true']])
        
        # Set message filter level to INFO to capture application logs
        AdminConfig.modify(rasLoggingService, [['messageFilterLevel', 'INFO']])
        
        print("✅ RAS Logging Service enabled successfully")
        
        # Get the service log configuration
        serviceLog = AdminConfig.showAttribute(rasLoggingService, 'serviceLog')
        if serviceLog:
            # Enable the service log
            AdminConfig.modify(serviceLog, [['enabled', 'true']])
            print("✅ Service log enabled")
        
        # Save the configuration
        print("Saving configuration...")
        AdminConfig.save()
        print("✅ Configuration saved successfully")
        
        print("")
        print("=== Configuration Summary ===")
        print("✅ RAS Logging Service: ENABLED")
        print("✅ Message Filter Level: INFO")
        print("✅ Service Log: ENABLED")
        print("✅ SystemOut.log: Will be populated after server restart")
        print("✅ SystemErr.log: Will be populated after server restart")
        print("")
        print("🔄 Please restart the WebSphere server to apply changes:")
        print("   docker restart appserver")
        
    else:
        print("❌ Error: Could not find RAS Logging Service")
        exit(1)
        
except Exception as e:
    print("❌ Error occurred: " + str(e))
    import traceback
    traceback.print_exc()
    exit(1)

print("=== RAS Logging Configuration Complete ===")
