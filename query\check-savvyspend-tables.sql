-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Show all tables owned by SAVVYSPEND schema
SELECT table_name, 
       num_rows,
       last_analyzed
FROM user_tables
ORDER BY table_name;

-- Alternative way to see tables with owner
SELECT owner, table_name
FROM all_tables 
WHERE owner = 'SAVVYSPEND'
ORDER BY table_name;

-- Check specifically for users table structure
SELECT 'USERS table structure:' AS info FROM dual;
DESCRIBE users;

-- Check what data is in users table
SELECT 'Current users in database:' AS info FROM dual;
SELECT * FROM users ORDER BY user_id;

-- Check for other important tables
SELECT 'Checking for other application tables:' AS info FROM dual;

-- Check if expenses table exists
SELECT COUNT(*) AS expenses_table_exists 
FROM user_tables 
WHERE table_name = 'EXPENSES';

-- Check if budgets table exists  
SELECT COUNT(*) AS budgets_table_exists 
FROM user_tables 
WHERE table_name = 'BUDGETS';

-- Check if categories table exists
SELECT COUNT(*) AS categories_table_exists 
FROM user_tables 
WHERE table_name = 'CATEGORIES';

-- Check if goals table exists
SELECT COUNT(*) AS goals_table_exists 
FROM user_tables 
WHERE table_name = 'GOALS';

-- Show sequences owned by SAVVYSPEND
SELECT 'Sequences in SAVVYSPEND schema:' AS info FROM dual;
SELECT sequence_name, last_number
FROM user_sequences
ORDER BY sequence_name;

EXIT;
