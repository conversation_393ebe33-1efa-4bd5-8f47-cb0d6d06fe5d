-- =====================================================
-- SavvySpend Pro - Sample Data for User 'sumit'
-- =====================================================
-- This script inserts comprehensive sample data for testing
-- the dynamic data functionality after removing dummy data
-- =====================================================

-- Clean up existing data for user 'sumit' (if any)
DELETE FROM budgets WHERE user_id = 1001;
DELETE FROM expenses WHERE user_id = 1001;
DELETE FROM categories WHERE user_id = 1001;
DELETE FROM users WHERE user_id = 1001;

-- Reset sequences (adjust based on your database)
-- For H2 Database:
-- ALTER SEQUENCE category_seq RESTART WITH 1;
-- ALTER SEQUENCE expense_seq RESTART WITH 1;
-- ALTER SEQUENCE budget_seq RESTART WITH 1;

-- =====================================================
-- 1. INSERT USER DATA
-- =====================================================
INSERT INTO users (
    user_id, username, email, password_hash, first_name, last_name, 
    role, is_active, preferred_currency, created_at, updated_at
) VALUES (
    1001, 
    'sumit', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqIcxkZDHKUYpY/Jo8.W6wHbBZWO2', -- password: 'password123'
    'Sumit', 
    'Kumar', 
    'USER', 
    true, 
    'USD', 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. INSERT CATEGORIES
-- =====================================================
INSERT INTO categories (id, name, description, color, user_id, created_at, updated_at) VALUES
(1001, 'Food & Dining', 'Restaurants, groceries, and food delivery', '#FF6B6B', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1002, 'Transportation', 'Gas, public transport, car maintenance', '#4ECDC4', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1003, 'Entertainment', 'Movies, games, streaming services', '#45B7D1', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1004, 'Shopping', 'Clothing, electronics, household items', '#96CEB4', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1005, 'Utilities', 'Electricity, water, internet, phone', '#FFEAA7', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1006, 'Healthcare', 'Medical expenses, pharmacy, insurance', '#DDA0DD', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1007, 'Education', 'Books, courses, training', '#98D8C8', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1008, 'Travel', 'Vacation, business trips, hotels', '#F7DC6F', 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- =====================================================
-- 3. INSERT EXPENSES (Last 3 months of data)
-- =====================================================

-- November 2024 Expenses
INSERT INTO expenses (id, amount, description, expense_date, payment_method, is_recurring, notes, category_id, user_id, created_at, updated_at) VALUES
-- Food & Dining
(2001, 45.50, 'Grocery shopping at Walmart', '2024-11-01', 'Credit Card', false, 'Weekly groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2002, 25.75, 'Lunch at Italian restaurant', '2024-11-03', 'Debit Card', false, 'Business lunch', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2003, 12.99, 'Coffee and pastry', '2024-11-05', 'Cash', false, 'Morning coffee', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2004, 67.80, 'Dinner with friends', '2024-11-08', 'Credit Card', false, 'Weekend dinner', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2005, 38.25, 'Grocery shopping', '2024-11-10', 'Debit Card', false, 'Weekly groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2006, 15.50, 'Fast food lunch', '2024-11-12', 'Credit Card', false, 'Quick lunch', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2007, 89.30, 'Family dinner', '2024-11-15', 'Credit Card', false, 'Special occasion', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2008, 42.60, 'Grocery shopping', '2024-11-18', 'Debit Card', false, 'Weekly groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2009, 28.75, 'Pizza delivery', '2024-11-22', 'Credit Card', false, 'Weekend treat', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2010, 55.40, 'Thanksgiving groceries', '2024-11-25', 'Credit Card', false, 'Holiday shopping', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Transportation
(2011, 65.00, 'Gas fill-up', '2024-11-02', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2012, 8.50, 'Bus fare', '2024-11-04', 'Cash', false, 'Daily commute', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2013, 72.30, 'Gas fill-up', '2024-11-09', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2014, 125.00, 'Car maintenance', '2024-11-14', 'Debit Card', false, 'Oil change and inspection', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2015, 68.75, 'Gas fill-up', '2024-11-16', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2016, 15.00, 'Parking fee', '2024-11-20', 'Cash', false, 'Downtown parking', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2017, 71.20, 'Gas fill-up', '2024-11-23', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Entertainment
(2018, 15.99, 'Netflix subscription', '2024-11-01', 'Credit Card', true, 'Monthly streaming', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2019, 12.50, 'Movie ticket', '2024-11-06', 'Credit Card', false, 'Weekend movie', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2020, 9.99, 'Spotify premium', '2024-11-01', 'Credit Card', true, 'Monthly music', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2021, 25.00, 'Concert ticket', '2024-11-13', 'Credit Card', false, 'Local band concert', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2022, 18.75, 'Gaming purchase', '2024-11-19', 'Credit Card', false, 'New game download', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Shopping
(2023, 89.99, 'Winter jacket', '2024-11-07', 'Credit Card', false, 'Seasonal clothing', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2024, 35.50, 'Household supplies', '2024-11-11', 'Debit Card', false, 'Cleaning supplies', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2025, 156.75, 'Electronics purchase', '2024-11-17', 'Credit Card', false, 'New headphones', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2026, 42.30, 'Books and magazines', '2024-11-21', 'Credit Card', false, 'Reading material', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Utilities
(2027, 85.50, 'Electricity bill', '2024-11-05', 'Bank Transfer', true, 'Monthly utility', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2028, 45.00, 'Internet bill', '2024-11-10', 'Credit Card', true, 'Monthly internet', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2029, 32.75, 'Water bill', '2024-11-15', 'Bank Transfer', true, 'Monthly water', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2030, 55.25, 'Phone bill', '2024-11-20', 'Credit Card', true, 'Monthly mobile', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- December 2024 Expenses
INSERT INTO expenses (id, amount, description, expense_date, payment_method, is_recurring, notes, category_id, user_id, created_at, updated_at) VALUES
-- Food & Dining
(2031, 52.30, 'Grocery shopping', '2024-12-01', 'Credit Card', false, 'Weekly groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2032, 78.90, 'Holiday dinner', '2024-12-05', 'Credit Card', false, 'Christmas celebration', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2033, 34.75, 'Lunch meeting', '2024-12-08', 'Credit Card', false, 'Business lunch', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2034, 95.60, 'Family gathering', '2024-12-15', 'Credit Card', false, 'Holiday party', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2035, 41.25, 'Grocery shopping', '2024-12-18', 'Debit Card', false, 'Weekly groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2036, 67.80, 'New Year dinner', '2024-12-31', 'Credit Card', false, 'Celebration dinner', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Transportation
(2037, 73.50, 'Gas fill-up', '2024-12-03', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2038, 69.25, 'Gas fill-up', '2024-12-10', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2039, 25.00, 'Airport parking', '2024-12-20', 'Credit Card', false, 'Holiday travel', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2040, 75.80, 'Gas fill-up', '2024-12-28', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Entertainment
(2041, 15.99, 'Netflix subscription', '2024-12-01', 'Credit Card', true, 'Monthly streaming', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2042, 9.99, 'Spotify premium', '2024-12-01', 'Credit Card', true, 'Monthly music', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2043, 45.00, 'Holiday movie marathon', '2024-12-12', 'Credit Card', false, 'Family entertainment', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2044, 32.50, 'Gaming subscription', '2024-12-15', 'Credit Card', false, 'Holiday gaming', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Shopping
(2045, 234.75, 'Holiday gifts', '2024-12-10', 'Credit Card', false, 'Christmas presents', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2046, 89.50, 'Winter clothing', '2024-12-14', 'Credit Card', false, 'Seasonal shopping', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2047, 156.30, 'Holiday decorations', '2024-12-18', 'Credit Card', false, 'Christmas decor', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Utilities
(2048, 92.75, 'Electricity bill', '2024-12-05', 'Bank Transfer', true, 'Monthly utility', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2049, 45.00, 'Internet bill', '2024-12-10', 'Credit Card', true, 'Monthly internet', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2050, 38.50, 'Water bill', '2024-12-15', 'Bank Transfer', true, 'Monthly water', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2051, 55.25, 'Phone bill', '2024-12-20', 'Credit Card', true, 'Monthly mobile', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Healthcare
(2052, 125.00, 'Doctor visit', '2024-12-08', 'Credit Card', false, 'Annual checkup', 1006, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2053, 45.75, 'Pharmacy', '2024-12-12', 'Credit Card', false, 'Prescription medication', 1006, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Travel
(2054, 450.00, 'Holiday flight', '2024-12-20', 'Credit Card', false, 'Christmas travel', 1008, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2055, 180.00, 'Hotel stay', '2024-12-22', 'Credit Card', false, 'Holiday accommodation', 1008, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- January 2025 Expenses (Current month)
INSERT INTO expenses (id, amount, description, expense_date, payment_method, is_recurring, notes, category_id, user_id, created_at, updated_at) VALUES
-- Food & Dining
(2056, 48.75, 'Grocery shopping', '2025-01-02', 'Credit Card', false, 'New Year groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2057, 32.50, 'Lunch out', '2025-01-05', 'Credit Card', false, 'Weekend lunch', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2058, 67.80, 'Dinner date', '2025-01-08', 'Credit Card', false, 'Special dinner', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2059, 41.25, 'Grocery shopping', '2025-01-10', 'Debit Card', false, 'Weekly groceries', 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Transportation
(2060, 71.50, 'Gas fill-up', '2025-01-03', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2061, 68.75, 'Gas fill-up', '2025-01-09', 'Credit Card', false, 'Weekly gas', 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Entertainment
(2062, 15.99, 'Netflix subscription', '2025-01-01', 'Credit Card', true, 'Monthly streaming', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2063, 9.99, 'Spotify premium', '2025-01-01', 'Credit Card', true, 'Monthly music', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2064, 22.50, 'Movie tickets', '2025-01-06', 'Credit Card', false, 'Weekend movie', 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Shopping
(2065, 125.50, 'Winter sale shopping', '2025-01-04', 'Credit Card', false, 'Post-holiday sales', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2066, 78.25, 'Home organization', '2025-01-07', 'Credit Card', false, 'New Year organization', 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Utilities
(2067, 88.50, 'Electricity bill', '2025-01-05', 'Bank Transfer', true, 'Monthly utility', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2068, 45.00, 'Internet bill', '2025-01-10', 'Credit Card', true, 'Monthly internet', 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Education
(2069, 199.99, 'Online course', '2025-01-08', 'Credit Card', false, 'Professional development', 1007, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- =====================================================
-- 4. INSERT BUDGETS (Current month - January 2025)
-- =====================================================
INSERT INTO budgets (id, name, budget_amount, description, start_date, end_date, period_type, alert_threshold, is_active, category_id, user_id, created_at, updated_at) VALUES
(3001, 'Food & Dining Budget', 500.00, 'Monthly budget for food and dining expenses', '2025-01-01', '2025-01-31', 'MONTHLY', 80.00, true, 1001, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3002, 'Transportation Budget', 300.00, 'Monthly budget for transportation costs', '2025-01-01', '2025-01-31', 'MONTHLY', 85.00, true, 1002, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3003, 'Entertainment Budget', 150.00, 'Monthly budget for entertainment and leisure', '2025-01-01', '2025-01-31', 'MONTHLY', 75.00, true, 1003, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3004, 'Shopping Budget', 400.00, 'Monthly budget for shopping and miscellaneous purchases', '2025-01-01', '2025-01-31', 'MONTHLY', 90.00, true, 1004, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3005, 'Utilities Budget', 250.00, 'Monthly budget for utilities and bills', '2025-01-01', '2025-01-31', 'MONTHLY', 95.00, true, 1005, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3006, 'Healthcare Budget', 200.00, 'Monthly budget for healthcare expenses', '2025-01-01', '2025-01-31', 'MONTHLY', 70.00, true, 1006, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3007, 'Education Budget', 300.00, 'Monthly budget for education and learning', '2025-01-01', '2025-01-31', 'MONTHLY', 80.00, true, 1007, 1001, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- =====================================================
-- 5. VERIFICATION QUERIES
-- =====================================================
-- Uncomment these to verify the data after insertion:

/*
-- Check user data
SELECT * FROM users WHERE username = 'sumit';

-- Check categories
SELECT * FROM categories WHERE user_id = 1001;

-- Check expenses summary
SELECT 
    c.name as category,
    COUNT(e.id) as expense_count,
    SUM(e.amount) as total_amount
FROM expenses e
JOIN categories c ON e.category_id = c.id
WHERE e.user_id = 1001
GROUP BY c.name
ORDER BY total_amount DESC;

-- Check budgets
SELECT 
    b.name,
    b.budget_amount,
    c.name as category
FROM budgets b
JOIN categories c ON b.category_id = c.id
WHERE b.user_id = 1001 AND b.is_active = true;

-- Check current month spending vs budget
SELECT 
    c.name as category,
    b.budget_amount,
    COALESCE(SUM(e.amount), 0) as spent_amount,
    (COALESCE(SUM(e.amount), 0) / b.budget_amount * 100) as percentage_used
FROM budgets b
JOIN categories c ON b.category_id = c.id
LEFT JOIN expenses e ON e.category_id = c.id 
    AND e.user_id = b.user_id 
    AND e.expense_date >= b.start_date 
    AND e.expense_date <= b.end_date
WHERE b.user_id = 1001 AND b.is_active = true
GROUP BY c.name, b.budget_amount
ORDER BY percentage_used DESC;
*/

-- =====================================================
-- DATA SUMMARY
-- =====================================================
-- User: sumit (ID: 1001)
-- Categories: 8 categories
-- Expenses: 69 expenses across 3 months (Nov 2024 - Jan 2025)
-- Budgets: 7 active budgets for January 2025
-- 
-- Total Spending by Month:
-- November 2024: ~$1,421
-- December 2024: ~$1,756  
-- January 2025: ~$677 (partial month)
--
-- Budget vs Actual (January 2025):
-- Food & Dining: $190.30 / $500 (38%)
-- Transportation: $140.25 / $300 (47%)
-- Entertainment: $48.48 / $150 (32%)
-- Shopping: $203.75 / $400 (51%)
-- Utilities: $133.50 / $250 (53%)
-- Education: $199.99 / $300 (67%)
-- Healthcare: $0 / $200 (0%)
-- =====================================================