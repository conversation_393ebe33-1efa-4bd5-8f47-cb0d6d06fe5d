package com.todoapp.messaging;

import com.todoapp.entity.Todo;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.ejb.Singleton;
import javax.ejb.Startup;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Todo Messaging Service
 * Manages the lifecycle of message producers and consumers
 * Provides a unified interface for asynchronous todo operations
 */
@Singleton
@Startup
public class TodoMessagingService {
    
    private static final Logger logger = Logger.getLogger(TodoMessagingService.class.getName());
    
    private TodoMessageProducer messageProducer;
    private TodoMessageConsumer messageConsumer;
    private boolean messagingEnabled = true;
    
    /**
     * Initialize messaging service on application startup
     */
    @PostConstruct
    public void initialize() {
        try {
            logger.info("🚀 Initializing Todo Messaging Service...");
            
            // Check if messaging is enabled
            String messagingEnabledProperty = System.getProperty("todo.messaging.enabled", "true");
            messagingEnabled = Boolean.parseBoolean(messagingEnabledProperty);
            
            if (!messagingEnabled) {
                logger.info("⚠️ Todo messaging is disabled via system property");
                return;
            }
            
            // Initialize message producer
            messageProducer = new TodoMessageProducer();
            logger.info("✅ Message Producer initialized");
            
            // Initialize and start message consumer
            messageConsumer = new TodoMessageConsumer();
            messageConsumer.startConsuming();
            logger.info("✅ Message Consumer started");
            
            logger.info("🎉 Todo Messaging Service initialized successfully");
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Failed to initialize Todo Messaging Service", e);
            messagingEnabled = false;
            // Don't throw exception - allow application to start without messaging
        }
    }
    
    /**
     * Cleanup messaging service on application shutdown
     */
    @PreDestroy
    public void cleanup() {
        try {
            logger.info("🛑 Shutting down Todo Messaging Service...");
            
            if (messageConsumer != null && messageConsumer.isRunning()) {
                messageConsumer.stopConsuming();
                logger.info("✅ Message Consumer stopped");
            }
            
            logger.info("✅ Todo Messaging Service shutdown complete");
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Error during messaging service cleanup", e);
        }
    }
    
    /**
     * Send asynchronous create todo message
     */
    public void sendCreateTodoAsync(Todo todo, String userId) {
        if (!messagingEnabled || messageProducer == null) {
            logger.warning("⚠️ Messaging not available, skipping async create");
            return;
        }
        
        try {
            messageProducer.sendCreateTodoMessage(todo, userId);
            logger.info("📤 Async create message sent for todo: " + todo.getTitle());
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to send async create message", e);
            // Don't throw exception - operation should continue synchronously
        }
    }
    
    /**
     * Send asynchronous update todo message
     */
    public void sendUpdateTodoAsync(Todo todo, String userId) {
        if (!messagingEnabled || messageProducer == null) {
            logger.warning("⚠️ Messaging not available, skipping async update");
            return;
        }
        
        try {
            messageProducer.sendUpdateTodoMessage(todo, userId);
            logger.info("📤 Async update message sent for todo: " + todo.getTitle());
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to send async update message", e);
            // Don't throw exception - operation should continue synchronously
        }
    }
    
    /**
     * Send asynchronous toggle todo message
     */
    public void sendToggleTodoAsync(Long todoId, String userId) {
        if (!messagingEnabled || messageProducer == null) {
            logger.warning("⚠️ Messaging not available, skipping async toggle");
            return;
        }
        
        try {
            messageProducer.sendToggleTodoMessage(todoId, userId);
            logger.info("📤 Async toggle message sent for todo ID: " + todoId);
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to send async toggle message", e);
            // Don't throw exception - operation should continue synchronously
        }
    }
    
    /**
     * Send asynchronous delete todo message
     */
    public void sendDeleteTodoAsync(Long todoId, String userId) {
        if (!messagingEnabled || messageProducer == null) {
            logger.warning("⚠️ Messaging not available, skipping async delete");
            return;
        }
        
        try {
            messageProducer.sendDeleteTodoMessage(todoId, userId);
            logger.info("📤 Async delete message sent for todo ID: " + todoId);
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to send async delete message", e);
            // Don't throw exception - operation should continue synchronously
        }
    }
    
    /**
     * Check if messaging is enabled and available
     */
    public boolean isMessagingAvailable() {
        return messagingEnabled && messageProducer != null && 
               messageConsumer != null && messageConsumer.isRunning();
    }
    
    /**
     * Get messaging service status
     */
    public String getMessagingStatus() {
        if (!messagingEnabled) {
            return "DISABLED";
        } else if (messageProducer == null || messageConsumer == null) {
            return "NOT_INITIALIZED";
        } else if (!messageConsumer.isRunning()) {
            return "CONSUMER_STOPPED";
        } else {
            return "ACTIVE";
        }
    }
    
    /**
     * Enable messaging (runtime control)
     */
    public void enableMessaging() {
        if (!messagingEnabled) {
            messagingEnabled = true;
            try {
                initialize();
                logger.info("✅ Messaging enabled at runtime");
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Failed to enable messaging at runtime", e);
                messagingEnabled = false;
            }
        }
    }
    
    /**
     * Disable messaging (runtime control)
     */
    public void disableMessaging() {
        if (messagingEnabled) {
            messagingEnabled = false;
            cleanup();
            logger.info("⚠️ Messaging disabled at runtime");
        }
    }
    
    /**
     * Get message producer (for advanced usage)
     */
    public TodoMessageProducer getMessageProducer() {
        return messageProducer;
    }
    
    /**
     * Get message consumer (for advanced usage)
     */
    public TodoMessageConsumer getMessageConsumer() {
        return messageConsumer;
    }
}
