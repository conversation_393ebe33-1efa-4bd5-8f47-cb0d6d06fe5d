-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Method 1: Copy password hash from demo user to johndoe
UPDATE users 
SET password_hash = (
    SELECT password_hash 
    FROM users 
    WHERE username = 'demo'
)
WHERE username = 'johndo<PERSON>';

-- Check if the update was successful
SELECT 'Updated ' || SQL%ROWCOUNT || ' row(s)' AS update_result FROM dual;

-- Commit the changes
COMMIT;

-- Verify both users now have the same password hash
SELECT username, password_hash, 
       CASE 
         WHEN password_hash = (SELECT password_hash FROM users WHERE username = 'demo') 
         THEN 'MATCHES DEMO' 
         ELSE 'DIFFERENT' 
       END AS password_status
FROM users 
WHERE username IN ('demo', 'johndoe')
ORDER BY username;

-- Alternative Method 2: If you know the demo user's password hash value
-- Uncomment and modify the hash value below if needed:
/*
UPDATE users 
SET password_hash = 'your_demo_password_hash_here'
WHERE username = 'johndo<PERSON>';
COMMIT;
*/

-- Method 3: If you want to set a specific password hash (e.g., for 'demo123')
-- This is a bcrypt hash for password 'demo123' - replace with your actual hash
/*
UPDATE users 
SET password_hash = '$2a$10$example.hash.here'
WHERE username = 'johndoe';
COMMIT;
*/

EXIT;
