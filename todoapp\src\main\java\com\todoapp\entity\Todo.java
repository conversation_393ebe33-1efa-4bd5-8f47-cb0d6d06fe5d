package com.todoapp.entity;

import java.sql.Timestamp;
import java.sql.Date;

/**
 * Todo Entity Class
 * Represents a to-do item in the database
 */
public class Todo {
    private Long todoId;
    private String title;
    private String description;
    private boolean completed;
    private String priority;
    private Timestamp createdDate;
    private Timestamp updatedDate;
    private Date dueDate;
    private Long userId;

    // Default constructor
    public Todo() {
        this.completed = false;
        this.priority = "MEDIUM";
        this.userId = 1L; // Default user
    }

    // Constructor with required fields
    public Todo(String title, String description) {
        this();
        this.title = title;
        this.description = description;
    }

    // Constructor with all fields except auto-generated ones
    public Todo(String title, String description, String priority, Date dueDate) {
        this(title, description);
        this.priority = priority;
        this.dueDate = dueDate;
    }

    // Getters and Setters
    public Long getTodoId() {
        return todoId;
    }

    public void setTodoId(Long todoId) {
        this.todoId = todoId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isCompleted() {
        return completed;
    }

    public void setCompleted(boolean completed) {
        this.completed = completed;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public Timestamp getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Timestamp updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    // Utility methods
    public boolean isOverdue() {
        if (dueDate == null || completed) {
            return false;
        }
        return dueDate.before(new Date(System.currentTimeMillis()));
    }

    public String getPriorityBadgeClass() {
        switch (priority.toUpperCase()) {
            case "HIGH":
                return "bg-danger";
            case "MEDIUM":
                return "bg-warning";
            case "LOW":
                return "bg-success";
            default:
                return "bg-secondary";
        }
    }

    public String getStatusBadgeClass() {
        return completed ? "bg-success" : "bg-secondary";
    }

    public String getStatusText() {
        return completed ? "Completed" : "Pending";
    }

    @Override
    public String toString() {
        return "Todo{" +
                "todoId=" + todoId +
                ", title='" + title + '\'' +
                ", completed=" + completed +
                ", priority='" + priority + '\'' +
                ", dueDate=" + dueDate +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Todo todo = (Todo) o;
        return todoId != null && todoId.equals(todo.todoId);
    }

    @Override
    public int hashCode() {
        return todoId != null ? todoId.hashCode() : 0;
    }
}
