#!/bin/bash

# ActiveMQ Integration Validation Script
# This script performs basic validation of the ActiveMQ setup

echo "🧪 ActiveMQ Integration Validation"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name - $message"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name - $message"
        ((TESTS_FAILED++))
    fi
}

# Function to test HTTP endpoint
test_http_endpoint() {
    local url="$1"
    local expected_code="$2"
    local timeout="$3"
    
    if [ -z "$timeout" ]; then
        timeout=10
    fi
    
    response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$timeout" "$url" 2>/dev/null)
    
    if [ "$response_code" = "$expected_code" ]; then
        echo "PASS"
    else
        echo "FAIL"
    fi
}

# Function to test HTTP endpoint with auth
test_http_endpoint_auth() {
    local url="$1"
    local username="$2"
    local password="$3"
    local expected_code="$4"
    local timeout="$5"
    
    if [ -z "$timeout" ]; then
        timeout=10
    fi
    
    response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$timeout" -u "$username:$password" "$url" 2>/dev/null)
    
    if [ "$response_code" = "$expected_code" ]; then
        echo "PASS"
    else
        echo "FAIL"
    fi
}

echo ""
echo "🔍 Testing Infrastructure Components..."
echo "======================================="

# Test 1: Docker containers are running
echo ""
echo "📦 Testing Docker Containers..."

# Check ActiveMQ container
if docker ps | grep -q "activemq.*Up"; then
    print_result "ActiveMQ Container" "PASS" "Container is running"
else
    print_result "ActiveMQ Container" "FAIL" "Container is not running"
fi

# Check Oracle container
if docker ps | grep -q "oracle.*Up"; then
    print_result "Oracle Container" "PASS" "Container is running"
else
    print_result "Oracle Container" "FAIL" "Container is not running"
fi

# Check WebSphere container
if docker ps | grep -q "appserver.*Up"; then
    print_result "WebSphere Container" "PASS" "Container is running"
else
    print_result "WebSphere Container" "FAIL" "Container is not running"
fi

# Test 2: Network connectivity
echo ""
echo "🌐 Testing Network Connectivity..."

# Test ActiveMQ web console
result=$(test_http_endpoint_auth "http://localhost:8161/admin/" "admin" "admin123" "200" 15)
print_result "ActiveMQ Web Console" "$result" "http://localhost:8161/admin/"

# Test ActiveMQ JMS port (basic connectivity)
if timeout 5 bash -c "</dev/tcp/localhost/61616" 2>/dev/null; then
    print_result "ActiveMQ JMS Port" "PASS" "Port 61616 is accessible"
else
    print_result "ActiveMQ JMS Port" "FAIL" "Port 61616 is not accessible"
fi

# Test WebSphere admin console
result=$(test_http_endpoint "https://localhost:9043/ibm/console/" "200" 15)
if [ "$result" = "FAIL" ]; then
    # Try without SSL verification
    response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 15 -k "https://localhost:9043/ibm/console/" 2>/dev/null)
    if [ "$response_code" = "200" ]; then
        result="PASS"
    fi
fi
print_result "WebSphere Admin Console" "$result" "https://localhost:9043/ibm/console/"

# Test Oracle database port
if timeout 5 bash -c "</dev/tcp/localhost/1521" 2>/dev/null; then
    print_result "Oracle Database Port" "PASS" "Port 1521 is accessible"
else
    print_result "Oracle Database Port" "FAIL" "Port 1521 is not accessible"
fi

# Test 3: ActiveMQ specific tests
echo ""
echo "📨 Testing ActiveMQ Functionality..."

# Check ActiveMQ broker status via REST API
activemq_status=$(curl -s -u admin:admin123 "http://localhost:8161/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost" 2>/dev/null | grep -o '"BrokerName":"[^"]*"' | cut -d'"' -f4)

if [ "$activemq_status" = "localhost" ]; then
    print_result "ActiveMQ Broker Status" "PASS" "Broker is active and responding"
else
    print_result "ActiveMQ Broker Status" "FAIL" "Broker is not responding correctly"
fi

# Check if ActiveMQ queues can be accessed
queue_info=$(curl -s -u admin:admin123 "http://localhost:8161/admin/xml/queues.jsp" 2>/dev/null)
if echo "$queue_info" | grep -q "queue"; then
    print_result "ActiveMQ Queue Access" "PASS" "Queue information is accessible"
else
    print_result "ActiveMQ Queue Access" "FAIL" "Cannot access queue information"
fi

# Test 4: Database connectivity
echo ""
echo "💾 Testing Database Connectivity..."

# Test Oracle database connection
db_test=$(docker exec oracle bash -c "echo 'SELECT 1 FROM DUAL;' | sqlplus -S system/SavvySpend123@XEPDB1" 2>/dev/null | grep -c "1")
if [ "$db_test" = "1" ]; then
    print_result "Oracle Database Connection" "PASS" "Database is accessible"
else
    print_result "Oracle Database Connection" "FAIL" "Cannot connect to database"
fi

# Test todos table exists
table_test=$(docker exec oracle bash -c "echo 'SELECT COUNT(*) FROM todos;' | sqlplus -S system/SavvySpend123@XEPDB1" 2>/dev/null | tail -1 | tr -d ' ')
if [[ "$table_test" =~ ^[0-9]+$ ]]; then
    print_result "Todos Table Access" "PASS" "Todos table exists and is accessible"
else
    print_result "Todos Table Access" "FAIL" "Cannot access todos table"
fi

# Test 5: WebSphere application deployment
echo ""
echo "🚀 Testing WebSphere Application..."

# Test if todo application is accessible
todo_app_result=$(test_http_endpoint "http://localhost:9080/todo-list-app/" "200" 20)
print_result "Todo Application" "$todo_app_result" "http://localhost:9080/todo-list-app/"

# Test resilience dashboard
resilience_result=$(test_http_endpoint "http://localhost:9080/todo-list-app/resilience/dashboard" "200" 15)
print_result "Resilience Dashboard" "$resilience_result" "http://localhost:9080/todo-list-app/resilience/dashboard"

# Test 6: ActiveMQ client libraries in WebSphere
echo ""
echo "📚 Testing ActiveMQ Client Libraries..."

# Check if ActiveMQ client JAR exists in WebSphere
if docker exec appserver test -f "/opt/IBM/WebSphere/AppServer/lib/activemq-client-5.18.3.jar" 2>/dev/null; then
    print_result "ActiveMQ Client JAR" "PASS" "activemq-client-5.18.3.jar is installed"
else
    print_result "ActiveMQ Client JAR" "FAIL" "activemq-client-5.18.3.jar is missing"
fi

# Check if Jackson JARs exist (needed for JSON processing)
if docker exec appserver test -f "/opt/IBM/WebSphere/AppServer/lib/jackson-core-2.15.2.jar" 2>/dev/null; then
    print_result "Jackson JSON Libraries" "PASS" "Jackson libraries are installed"
else
    print_result "Jackson JSON Libraries" "FAIL" "Jackson libraries are missing"
fi

# Test 7: System integration test
echo ""
echo "🔗 Testing System Integration..."

# Test if all components can communicate
if [ "$TESTS_FAILED" -eq 0 ]; then
    print_result "Overall System Health" "PASS" "All components are operational"
else
    print_result "Overall System Health" "FAIL" "Some components have issues"
fi

# Summary
echo ""
echo "📊 Test Summary"
echo "==============="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ "$TESTS_FAILED" -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! ActiveMQ integration is ready for use.${NC}"
    echo ""
    echo "🔗 Access Points:"
    echo "  • ActiveMQ Console: http://localhost:8161 (admin/admin123)"
    echo "  • Todo Application: http://localhost:9080/todo-list-app/"
    echo "  • Resilience Dashboard: http://localhost:9080/todo-list-app/resilience/dashboard"
    echo "  • WebSphere Console: https://localhost:9043/ibm/console/"
    echo ""
    echo "📝 Next Steps:"
    echo "  1. Test todo operations in the web application"
    echo "  2. Monitor message queues in ActiveMQ console"
    echo "  3. Test resilience features using the dashboard"
    echo "  4. Review the test plan in test-activemq-integration.md"
    
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some tests failed. Please check the issues above.${NC}"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "  1. Check container logs: docker logs <container_name>"
    echo "  2. Verify network connectivity between containers"
    echo "  3. Check configuration files for errors"
    echo "  4. Restart services if needed: docker-compose restart"
    
    exit 1
fi
