# 🔧 SavvySpend JNDI Configuration Guide

## 🎯 Objective
Configure WebSphere JNDI DataSource for SavvySpend application to fix the "missing table [budgets]" error.

## 📋 Prerequisites
✅ **Database Tables Created**: All required tables exist in XE container database (SYSTEM schema)
✅ **WebSphere Admin Console**: Accessible at https://localhost:9043/ibm/console
✅ **Oracle JDBC Driver**: Already installed in WebSphere

## 🔧 Step-by-Step JNDI Configuration

### **Step 1: Access WebSphere Admin Console**
1. Open: https://localhost:9043/ibm/console
2. Login with your WebSphere credentials
3. Navigate to **Resources** → **JDBC** → **Data sources**

### **Step 2: Create New Data Source**
1. Click **New** button
2. Enter the following details:

**Basic Information:**
- **Data source name**: `SavvySpendDataSource`
- **JNDI name**: `jdbc/SavvySpendDS`
- **Description**: `Oracle DataSource for SavvySpend Application`

**Database Type:**
- **Database type**: `Oracle`
- **Provider type**: `Oracle JDBC Driver`
- **Implementation type**: `Connection pool data source`

### **Step 3: Configure Database Connection**
**Connection Properties:**
- **URL**: `***********************************`
- **Data store helper class name**: `com.ibm.websphere.rsadapter.Oracle11gDataStoreHelper`

**Authentication:**
- **Component-managed authentication alias**: Leave blank
- **Container-managed authentication alias**: Leave blank
- **Authentication method**: `BASIC_PASSWORD`

**Custom Properties:**
Add these custom properties:
- **user**: `system`
- **password**: `SavvySpend123`
- **URL**: `***********************************`

### **Step 4: Configure Connection Pool**
**Connection Pool Settings:**
- **Minimum connections**: `5`
- **Maximum connections**: `20`
- **Connection timeout**: `30` seconds
- **Reap time**: `180` seconds
- **Unused timeout**: `1800` seconds
- **Aged timeout**: `0` seconds

### **Step 5: Test Connection**
1. Save the configuration
2. Click **Test connection** button
3. Verify connection is successful

### **Step 6: Apply and Save**
1. Click **Apply** and **Save**
2. Save to master configuration
3. Restart WebSphere server: `docker restart appserver`

## 📝 Alternative: Use Configuration Script

If you prefer automated configuration, copy and run this script:

```bash
# Copy the JNDI configuration script
docker cp configure_savvyspend_jndi.py appserver:/tmp/configure_savvyspend_jndi.py

# Run the configuration script (requires WebSphere admin credentials)
docker exec appserver /opt/IBM/WebSphere/AppServer/bin/wsadmin.sh -lang jython -f /tmp/configure_savvyspend_jndi.py
```

## 🔍 Verification Steps

### **1. Verify JNDI DataSource**
```bash
# Check if DataSource is created
docker exec appserver /opt/IBM/WebSphere/AppServer/bin/wsadmin.sh -lang jython -c "print AdminConfig.list('DataSource')"
```

### **2. Verify Database Tables**
```bash
# Confirm tables exist in correct database
docker exec oracle bash -c "echo 'SELECT table_name FROM user_tables WHERE table_name IN (\"BUDGETS\", \"USERS\", \"CATEGORIES\", \"EXPENSES\", \"GOALS\") ORDER BY table_name;' | sqlplus -S system/SavvySpend123@XE"
```

### **3. Test Application Startup**
After JNDI configuration:
1. Restart WebSphere: `docker restart appserver`
2. Monitor logs: `docker exec appserver tail -f /opt/IBM/WebSphere/AppServer/profiles/AppSrv01/logs/server1/SystemOut.log`
3. Look for successful application startup without "missing table [budgets]" error

## 🎯 Expected Results

### **Before JNDI Configuration:**
```
❌ Schema-validation: missing table [budgets]
❌ Application fails to start
❌ Direct JDBC connection issues
```

### **After JNDI Configuration:**
```
✅ JNDI DataSource: jdbc/SavvySpendDS
✅ Database connection via WebSphere connection pool
✅ All tables found in SYSTEM schema
✅ SavvySpend application starts successfully
✅ Application accessible at: http://localhost:9080/savvyspend/
```

## 🔧 Configuration Summary

**JNDI DataSource Details:**
- **Name**: SavvySpendDataSource
- **JNDI**: jdbc/SavvySpendDS
- **URL**: ***********************************
- **User**: system (SYSTEM schema)
- **Database**: XE container database
- **Tables**: BUDGETS, USERS, CATEGORIES, EXPENSES, GOALS

**Application Configuration:**
- **Remove**: Direct JDBC properties (url, username, password)
- **Add**: `spring.datasource.jndi-name=jdbc/SavvySpendDS`
- **Keep**: JPA/Hibernate validation settings

## 🚨 Troubleshooting

### **If JNDI Configuration Fails:**
1. Check WebSphere admin console for error messages
2. Verify Oracle JDBC driver is in WebSphere classpath
3. Confirm database connectivity from WebSphere container
4. Check authentication credentials

### **If Application Still Fails:**
1. Verify JNDI name matches exactly: `jdbc/SavvySpendDS`
2. Confirm application.properties uses JNDI configuration
3. Check that tables exist in correct schema (SYSTEM)
4. Review WebSphere SystemOut.log for detailed errors

## 📞 Next Steps

1. **Configure JNDI DataSource** using WebSphere admin console
2. **Update application configuration** to use JNDI
3. **Restart WebSphere server** to apply changes
4. **Test SavvySpend application** startup and functionality

---

**🎉 Once JNDI is configured, the SavvySpend application should start successfully and connect to the Oracle database via WebSphere's managed connection pool!**
