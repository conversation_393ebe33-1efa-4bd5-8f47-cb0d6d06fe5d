-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Check users table structure
DESCRIBE users;

-- Check table constraints
SELECT constraint_name, constraint_type, search_condition
FROM user_constraints
WHERE table_name = 'USERS';

-- Check indexes on users table
SELECT index_name, column_name, column_position
FROM user_ind_columns
WHERE table_name = 'USERS'
ORDER BY index_name, column_position;

-- Count total users
SELECT COUNT(*) AS total_users FROM users;

-- List all users with basic info
SELECT id, username, email, first_name, last_name, role, is_active 
FROM users
ORDER BY id;

-- Find specific user by username
SELECT * FROM users WHERE username = 'admin';

-- Find users by role
SELECT username, email, first_name, last_name
FROM users
WHERE role = 'ADMIN'
ORDER BY username;

-- Check active vs inactive users
SELECT is_active, COUNT(*) AS user_count
FROM users
GROUP BY is_active;

-- Find recently created users
SELECT username, email, created_at
FROM users
ORDER BY created_at DESC
FETCH FIRST 5 ROWS ONLY;

-- Check user preferences
SELECT username, preferred_currency
FROM users
ORDER BY username;

-- Find users with specific email domain
SELECT username, email
FROM users
WHERE email LIKE '%@savvyspend.com'
ORDER BY username;

-- Users with their expense counts
SELECT u.username, COUNT(e.id) AS expense_count
FROM users u
LEFT JOIN expenses e ON u.id = e.user_id
GROUP BY u.username
ORDER BY expense_count DESC;

-- Users with their budget counts
SELECT u.username, COUNT(b.id) AS budget_count
FROM users u
LEFT JOIN budgets b ON u.id = b.user_id
GROUP BY u.username
ORDER BY budget_count DESC;

-- Users who haven't logged any expenses
SELECT u.username, u.email
FROM users u
LEFT JOIN expenses e ON u.id = e.user_id
WHERE e.id IS NULL
ORDER BY u.username;

-- User registration trends by month
SELECT 
    EXTRACT(YEAR FROM created_at) AS year,
    EXTRACT(MONTH FROM created_at) AS month,
    COUNT(*) AS new_users
FROM users
GROUP BY EXTRACT(YEAR FROM created_at), EXTRACT(MONTH FROM created_at)
ORDER BY year, month;

-- Check when users were last updated
SELECT username, updated_at
FROM users
ORDER BY updated_at DESC;

EXIT;

