----Docker compose configuration--
system Savvyspend123 (System db id password) 
Create pdb xepdb1 (create Pluggable DB)
    environment:
      - ORACLE_PDB=XEPDB1
      - ORACLE_PWD=SavvySpend123
---Script from startup {init folder}-----
Create application user todouser with password (todouser/TodoAdmin123@XEPDB1;)

--IBM Websphere --
jndi name: jdbc/OracleDS
URL: ***************************************

--Application specific--
either in web.xml or application.properties mention jndi name (jdbc/OracleDS)