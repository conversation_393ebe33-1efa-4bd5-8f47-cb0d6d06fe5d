# Clean WebSphere Setup Guide

## 🎯 **Purpose**

This is a **clean WebSphere setup** that restores the working authentication that existed before JDBC JAR mounting issues. This setup uses **IBM's official container initialization** method.

## 🔧 **Key Differences from Previous Setup**

### ✅ **What's Fixed:**
- **No custom Dockerfile** - Uses IBM's official image directly
- **Environment variables** - Proper admin user setup via IBM's method
- **No JAR mounting** - Avoids authentication corruption
- **Clean configuration** - No XML parsing errors or corruption
- **Simple approach** - Minimal complexity, maximum reliability

### 📋 **Setup Approach:**
1. **Phase 1**: Get authentication working with clean setup
2. **Phase 2**: Add Oracle JARs properly after authentication is confirmed
3. **Phase 3**: Deploy applications and configure database connections

## 🚀 **Quick Start**

### **Step 1: Stop Current Setup**
```bash
# In the main directory
docker-compose down
```

### **Step 2: Start Clean Setup**
```bash
# Navigate to fresh setup
cd fresh-websphere-setup

# Start clean containers
docker-compose up -d

# Monitor startup (5-10 minutes)
docker-compose logs -f websphere
```

### **Step 3: Test Authentication**
```bash
# Wait for "Server server1 open for e-business" message
# Then test the setup
bash test-clean-setup.sh
```

### **Step 4: Access Admin Console**
- **URL**: https://localhost:9043/ibm/console
- **Username**: `wsadmin`
- **Password**: `was@123`

## 🔍 **Expected Results**

### ✅ **Should Work Immediately:**
- Admin console accessible
- Login with wsadmin:was@123
- No authentication errors
- Clean WebSphere startup
- No Oracle i18n errors (since no JARs mounted yet)

### 📊 **Verification Steps:**
1. Container starts successfully
2. WebSphere logs show "open for e-business"
3. Admin console loads at https://localhost:9043/ibm/console
4. Login works with wsadmin:was@123
5. No SECJ0118E errors in logs

## 🔄 **Phase 2: Adding Oracle JARs (After Authentication Works)**

Once authentication is confirmed working, we can add Oracle JARs properly:

### **Method 1: Runtime Addition**
```bash
# Copy JARs to running container
docker cp websphere/jdbc/orai18n.jar websphere-clean:/opt/IBM/WebSphere/AppServer/java/8.0/jre/lib/ext/
docker cp websphere/jdbc/ojdbc8.jar websphere-clean:/opt/IBM/WebSphere/AppServer/lib/

# Restart WebSphere
docker-compose restart websphere
```

### **Method 2: Volume Mounting (Careful)**
```yaml
# Add to docker-compose.yml volumes section ONLY after authentication works
- ./websphere/jdbc/orai18n.jar:/opt/IBM/WebSphere/AppServer/java/8.0/jre/lib/ext/orai18n.jar
- ./websphere/jdbc/ojdbc8.jar:/opt/IBM/WebSphere/AppServer/lib/ojdbc8.jar
```

## 🎯 **Success Criteria**

### **Phase 1 Success:**
- ✅ WebSphere starts cleanly
- ✅ Admin console accessible
- ✅ wsadmin:was@123 authentication works
- ✅ No authentication errors

### **Phase 2 Success:**
- ✅ Oracle JARs loaded
- ✅ Authentication still works
- ✅ No Oracle i18n errors
- ✅ Ready for database connections

## 🐛 **Troubleshooting**

### **If Authentication Fails:**
1. Check container logs: `docker-compose logs websphere`
2. Verify environment variables in docker-compose.yml
3. Ensure no volume conflicts with existing setup
4. Try restarting: `docker-compose restart websphere`

### **If Container Won't Start:**
1. Check port conflicts: `netstat -an | findstr 9043`
2. Verify Docker resources available
3. Check for volume conflicts
4. Try: `docker-compose down && docker-compose up -d`

## 📁 **File Structure**
```
fresh-websphere-setup/
├── docker-compose.yml          # Clean WebSphere setup
├── test-clean-setup.sh         # Test script
├── README.md                   # This guide
├── websphere/
│   └── jdbc/                   # Oracle JARs (for Phase 2)
├── haproxy/                    # Load balancer config
└── nginx/                      # Web server config
```

## 🎉 **Expected Outcome**

This setup should restore the **working wsadmin:was@123 authentication** that existed before the JDBC JAR mounting issues. Once confirmed working, we can carefully add Oracle JARs without breaking authentication.

**Goal**: Get back to a working state, then incrementally add complexity.
