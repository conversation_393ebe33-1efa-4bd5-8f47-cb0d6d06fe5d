-- =============================================================================
-- TO-DO LIST APPLICATION - DATABASE SCHEMA
-- =============================================================================
-- Creates the todos table in the existing Oracle database schema
-- Connects to Pluggable Database
-- =============================================================================

SET SERVEROUTPUT ON SIZE 1000000
SET ECHO ON

PROMPT =============================================================================;
PROMPT CREATING TO-DO LIST TABLE IN TODOUSER SCHEMA
PROMPT =============================================================================;

-- Connect to the pluggable database
ALTER SESSION SET CONTAINER = XEPDB1;

-- Check if todouser already exists, create only if not exists
DECLARE
    user_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM dba_users WHERE username = 'TODOUSER';

    IF user_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE USER todouser IDENTIFIED BY TodoAdmin123 DEFAULT TABLESPACE USERS TEMPORARY TABLESPACE TEMP';
        EXECUTE IMMEDIATE 'GRANT CONNECT, RESOURCE TO todouser';
        EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO todouser';
        EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO todouser';
        EXECUTE IMMEDIATE 'GRANT CREATE SEQUENCE TO todouser';
        EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO todouser';
        EXECUTE IMMEDIATE 'ALTER USER todouser QUOTA UNLIMITED ON USERS';
        DBMS_OUTPUT.PUT_LINE('✅ User todouser created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  User todouser already exists, skipping creation');
    END IF;
END;
/

-- Connect as the todouser
CONNECT todouser/TodoAdmin123@XEPDB1;

-- Check if todos table already exists, create only if not exists
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'TODOS';

    IF table_count = 0 THEN
        -- Create the todos table
        EXECUTE IMMEDIATE 'CREATE TABLE todos (
            todo_id NUMBER(19) NOT NULL,
            title VARCHAR2(255) NOT NULL,
            description CLOB,
            completed NUMBER(1) DEFAULT 0 NOT NULL,
            priority VARCHAR2(10) DEFAULT ''MEDIUM'' NOT NULL,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            due_date DATE,
            user_id NUMBER(19) DEFAULT 1 NOT NULL,
            CONSTRAINT pk_todos PRIMARY KEY (todo_id),
            CONSTRAINT chk_completed CHECK (completed IN (0, 1)),
            CONSTRAINT chk_priority CHECK (priority IN (''LOW'', ''MEDIUM'', ''HIGH'')),
            CONSTRAINT chk_title_not_empty CHECK (LENGTH(TRIM(title)) > 0)
        )';

        DBMS_OUTPUT.PUT_LINE('✅ Table todos created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table todos already exists, skipping creation');
    END IF;
END;
/

-- Check if sequence already exists, create only if not exists
DECLARE
    seq_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'TODO_SEQ';

    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE todo_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence todo_seq created successfully');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Sequence todo_seq already exists, skipping creation');
    END IF;
END;
/

-- Create or replace triggers (these can be safely recreated)
CREATE OR REPLACE TRIGGER trg_todos_id
    BEFORE INSERT ON todos
    FOR EACH ROW
BEGIN
    IF :NEW.todo_id IS NULL THEN
        :NEW.todo_id := todo_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_todos_updated_date
    BEFORE UPDATE ON todos
    FOR EACH ROW
BEGIN
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- Check and create indexes only if they don't exist
DECLARE
    idx_count NUMBER;
BEGIN
    -- Check idx_todos_completed
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_TODOS_COMPLETED';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_todos_completed ON todos(completed)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_todos_completed created');
    END IF;

    -- Check idx_todos_user_id
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_TODOS_USER_ID';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_todos_user_id ON todos(user_id)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_todos_user_id created');
    END IF;

    -- Check idx_todos_created_date
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_TODOS_CREATED_DATE';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_todos_created_date ON todos(created_date)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_todos_created_date created');
    END IF;

    -- Check idx_todos_priority
    SELECT COUNT(*) INTO idx_count FROM user_indexes WHERE index_name = 'IDX_TODOS_PRIORITY';
    IF idx_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_todos_priority ON todos(priority)';
        DBMS_OUTPUT.PUT_LINE('✅ Index idx_todos_priority created');
    END IF;
END;
/

-- Insert sample data only if table is empty
DECLARE
    data_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO data_count FROM todos;

    IF data_count = 0 THEN
        INSERT INTO todos (title, description, priority, due_date) VALUES
        ('Complete project documentation', 'Write comprehensive documentation for the new to-do application', 'HIGH', SYSDATE + 3);

        INSERT INTO todos (title, description, priority, completed) VALUES
        ('Review code changes', 'Review and approve pending pull requests', 'MEDIUM', 0);

        INSERT INTO todos (title, description, priority, completed) VALUES
        ('Setup development environment', 'Install and configure all necessary development tools', 'LOW', 1);

        INSERT INTO todos (title, description, priority, due_date) VALUES
        ('Prepare presentation', 'Create slides for the quarterly review meeting', 'HIGH', SYSDATE + 7);

        INSERT INTO todos (title, description, priority) VALUES
        ('Update dependencies', 'Update all project dependencies to latest versions', 'MEDIUM');

        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ Sample data inserted: 5 to-do items');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Sample data already exists (' || data_count || ' records), skipping insertion');
    END IF;
END;
/

PROMPT;
PROMPT =============================================================================;
PROMPT TO-DO LIST TABLE CREATED SUCCESSFULLY!;
PROMPT =============================================================================;
PROMPT;
PROMPT Table: todos;
PROMPT Columns:;
PROMPT - todo_id (Primary Key, Auto-increment);
PROMPT - title (Required, max 255 chars);
PROMPT - description (Optional, CLOB);
PROMPT - completed (0=Pending, 1=Completed);
PROMPT - priority (LOW, MEDIUM, HIGH);
PROMPT - created_date (Auto-set);
PROMPT - updated_date (Auto-updated);
PROMPT - due_date (Optional);
PROMPT - user_id (Default: 1);
PROMPT;
PROMPT Sample data inserted: 5 to-do items;
PROMPT =============================================================================;
