package com.todoapp.service;

import com.todoapp.entity.Todo;
import com.todoapp.messaging.TodoMessagingService;
import com.todoapp.resilience.DatabaseResilienceManager;
import com.todoapp.resilience.DatabaseResilienceManager.CircuitBreakerState;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Resilient Todo Service
 * Enhanced version of TodoService with database resilience features
 * including retry mechanisms, circuit breaker, and transaction management
 */
public class ResilientTodoService {
    
    private static final Logger logger = Logger.getLogger(ResilientTodoService.class.getName());
    private static final String JNDI_NAME = "jdbc/OracleDS";
    
    private final DataSource dataSource;
    private final DatabaseResilienceManager resilienceManager;
    private final TodoMessagingService messagingService;
    private final boolean asyncProcessingEnabled;
    
    public ResilientTodoService() {
        try {
            // Initialize JNDI DataSource
            Context ctx = new InitialContext();
            dataSource = (DataSource) ctx.lookup(JNDI_NAME);
            logger.info("✅ ResilientTodoService initialized with JNDI DataSource: " + JNDI_NAME);
            
            // Initialize resilience manager
            resilienceManager = new DatabaseResilienceManager(dataSource);
            logger.info("✅ Database resilience manager initialized");
            
            // Initialize messaging service
            try {
                messagingService = new TodoMessagingService();
                asyncProcessingEnabled = true;
                logger.info("✅ Messaging service initialized for async processing");
            } catch (Exception e) {
                logger.log(Level.WARNING, "⚠️ Failed to initialize messaging service, continuing without async processing", e);
                throw new RuntimeException("Messaging service required for resilient operations", e);
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "❌ Failed to initialize ResilientTodoService", e);
            throw new RuntimeException("Service initialization failed", e);
        }
    }
    
    /**
     * Get all todos with resilience
     */
    public List<Todo> getAllTodos(Long userId) {
        try {
            return resilienceManager.executeWithResilience(dataSource -> {
                List<Todo> todos = new ArrayList<>();
                String sql = "SELECT * FROM todos WHERE user_id = ? ORDER BY created_date DESC";
                
                try (Connection conn = dataSource.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {
                    
                    stmt.setLong(1, userId);
                    ResultSet rs = stmt.executeQuery();
                    
                    while (rs.next()) {
                        todos.add(mapResultSetToTodo(rs));
                    }
                    
                    logger.info("✅ Retrieved " + todos.size() + " todos for user " + userId);
                    return todos;
                }
            });
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "❌ Failed to retrieve todos with resilience", e);
            
            // If circuit breaker is open, try to get cached data or return empty list
            if (resilienceManager.getCircuitBreakerState() == CircuitBreakerState.OPEN) {
                logger.warning("🚨 Circuit breaker is open, returning empty todo list");
                return new ArrayList<>();
            }
            
            throw new RuntimeException("Failed to retrieve todos", e);
        }
    }
    
    /**
     * Create todo with resilience and async processing
     */
    public Todo createTodo(Todo todo) {
        try {
            return resilienceManager.executeWithTransaction(connection -> {
                String sql = "INSERT INTO todos (title, description, priority, due_date, user_id) VALUES (?, ?, ?, ?, ?)";
                
                try (PreparedStatement stmt = connection.prepareStatement(sql, new String[]{"todo_id"})) {
                    stmt.setString(1, todo.getTitle());
                    stmt.setString(2, todo.getDescription());
                    stmt.setString(3, todo.getPriority());
                    stmt.setDate(4, todo.getDueDate());
                    stmt.setLong(5, todo.getUserId());
                    
                    int rowsAffected = stmt.executeUpdate();
                    
                    if (rowsAffected > 0) {
                        ResultSet generatedKeys = stmt.getGeneratedKeys();
                        if (generatedKeys.next()) {
                            todo.setTodoId(generatedKeys.getLong(1));
                        }
                        
                        logger.info("✅ Created new todo with ID: " + todo.getTodoId());
                        
                        Todo createdTodo = getTodoById(todo.getTodoId());
                        
                        // Send async notification
                        if (asyncProcessingEnabled && messagingService != null) {
                            try {
                                messagingService.sendCreateTodoAsync(createdTodo, String.valueOf(todo.getUserId()));
                            } catch (Exception e) {
                                logger.log(Level.WARNING, "⚠️ Failed to send async create notification", e);
                                // Don't fail the operation due to messaging issues
                            }
                        }
                        
                        return createdTodo;
                    }
                    
                    throw new SQLException("Failed to create todo - no rows affected");
                }
            });
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "❌ Failed to create todo with resilience", e);
            
            // If circuit breaker is open, queue the operation for later processing
            if (resilienceManager.getCircuitBreakerState() == CircuitBreakerState.OPEN) {
                return handleCreateTodoFallback(todo);
            }
            
            throw new RuntimeException("Failed to create todo", e);
        }
    }
    
    /**
     * Update todo with resilience
     */
    public Todo updateTodo(Todo todo) {
        try {
            return resilienceManager.executeWithTransaction(connection -> {
                String sql = "UPDATE todos SET title = ?, description = ?, priority = ?, due_date = ?, completed = ? WHERE todo_id = ?";
                
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setString(1, todo.getTitle());
                    stmt.setString(2, todo.getDescription());
                    stmt.setString(3, todo.getPriority());
                    stmt.setDate(4, todo.getDueDate());
                    stmt.setInt(5, todo.isCompleted() ? 1 : 0);
                    stmt.setLong(6, todo.getTodoId());
                    
                    int rowsAffected = stmt.executeUpdate();
                    
                    if (rowsAffected > 0) {
                        logger.info("✅ Updated todo with ID: " + todo.getTodoId());
                        
                        Todo updatedTodo = getTodoById(todo.getTodoId());
                        
                        // Send async notification
                        if (asyncProcessingEnabled && messagingService != null) {
                            try {
                                messagingService.sendUpdateTodoAsync(updatedTodo, String.valueOf(todo.getUserId()));
                            } catch (Exception e) {
                                logger.log(Level.WARNING, "⚠️ Failed to send async update notification", e);
                            }
                        }
                        
                        return updatedTodo;
                    }
                    
                    throw new SQLException("Failed to update todo - no rows affected");
                }
            });
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "❌ Failed to update todo with resilience", e);
            
            if (resilienceManager.getCircuitBreakerState() == CircuitBreakerState.OPEN) {
                return handleUpdateTodoFallback(todo);
            }
            
            throw new RuntimeException("Failed to update todo", e);
        }
    }
    
    /**
     * Toggle todo status with resilience
     */
    public boolean toggleTodoStatus(Long todoId) {
        try {
            return resilienceManager.executeWithTransaction(connection -> {
                String sql = "UPDATE todos SET completed = CASE WHEN completed = 0 THEN 1 ELSE 0 END WHERE todo_id = ?";
                
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setLong(1, todoId);
                    int rowsAffected = stmt.executeUpdate();
                    
                    if (rowsAffected > 0) {
                        logger.info("✅ Toggled status for todo ID: " + todoId);
                        
                        // Send async notification
                        if (asyncProcessingEnabled && messagingService != null) {
                            try {
                                Todo todo = getTodoById(todoId);
                                if (todo != null) {
                                    messagingService.sendToggleTodoAsync(todoId, String.valueOf(todo.getUserId()));
                                }
                            } catch (Exception e) {
                                logger.log(Level.WARNING, "⚠️ Failed to send async toggle notification", e);
                            }
                        }
                        
                        return true;
                    }
                    
                    return false;
                }
            });
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "❌ Failed to toggle todo status with resilience", e);
            
            if (resilienceManager.getCircuitBreakerState() == CircuitBreakerState.OPEN) {
                return handleToggleTodoFallback(todoId);
            }
            
            throw new RuntimeException("Failed to toggle todo status", e);
        }
    }
    
    /**
     * Delete todo with resilience
     */
    public boolean deleteTodo(Long todoId) {
        try {
            // Get todo before deletion for async notification
            Todo todoToDelete = getTodoById(todoId);
            
            return resilienceManager.executeWithTransaction(connection -> {
                String sql = "DELETE FROM todos WHERE todo_id = ?";
                
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setLong(1, todoId);
                    int rowsAffected = stmt.executeUpdate();
                    
                    if (rowsAffected > 0) {
                        logger.info("✅ Deleted todo with ID: " + todoId);
                        
                        // Send async notification
                        if (asyncProcessingEnabled && messagingService != null && todoToDelete != null) {
                            try {
                                messagingService.sendDeleteTodoAsync(todoId, String.valueOf(todoToDelete.getUserId()));
                            } catch (Exception e) {
                                logger.log(Level.WARNING, "⚠️ Failed to send async delete notification", e);
                            }
                        }
                        
                        return true;
                    }
                    
                    return false;
                }
            });
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "❌ Failed to delete todo with resilience", e);
            
            if (resilienceManager.getCircuitBreakerState() == CircuitBreakerState.OPEN) {
                return handleDeleteTodoFallback(todoId);
            }
            
            throw new RuntimeException("Failed to delete todo", e);
        }
    }
    
    /**
     * Get todo by ID with resilience
     */
    public Todo getTodoById(Long todoId) {
        try {
            return resilienceManager.executeWithResilience(dataSource -> {
                String sql = "SELECT * FROM todos WHERE todo_id = ?";
                
                try (Connection conn = dataSource.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {
                    
                    stmt.setLong(1, todoId);
                    ResultSet rs = stmt.executeQuery();
                    
                    if (rs.next()) {
                        return mapResultSetToTodo(rs);
                    }
                    
                    return null;
                }
            });
        } catch (SQLException e) {
            logger.log(Level.WARNING, "❌ Failed to get todo by ID with resilience", e);
            return null;
        }
    }
    
    // Fallback methods for when circuit breaker is open
    
    private Todo handleCreateTodoFallback(Todo todo) {
        logger.warning("🚨 Circuit breaker open - queuing create operation for async processing");
        
        if (asyncProcessingEnabled && messagingService != null) {
            try {
                // Queue the operation for later processing when database is available
                messagingService.sendCreateTodoAsync(todo, String.valueOf(todo.getUserId()));
                logger.info("📤 Create operation queued for async processing");
                
                // Return a temporary todo with negative ID to indicate it's queued
                todo.setTodoId(-System.currentTimeMillis());
                return todo;
            } catch (Exception e) {
                logger.log(Level.SEVERE, "❌ Failed to queue create operation", e);
            }
        }
        
        throw new RuntimeException("Database unavailable and async processing failed");
    }
    
    private Todo handleUpdateTodoFallback(Todo todo) {
        logger.warning("🚨 Circuit breaker open - queuing update operation for async processing");
        
        if (asyncProcessingEnabled && messagingService != null) {
            try {
                messagingService.sendUpdateTodoAsync(todo, String.valueOf(todo.getUserId()));
                logger.info("📤 Update operation queued for async processing");
                return todo;
            } catch (Exception e) {
                logger.log(Level.SEVERE, "❌ Failed to queue update operation", e);
            }
        }
        
        throw new RuntimeException("Database unavailable and async processing failed");
    }
    
    private boolean handleToggleTodoFallback(Long todoId) {
        logger.warning("🚨 Circuit breaker open - queuing toggle operation for async processing");
        
        if (asyncProcessingEnabled && messagingService != null) {
            try {
                messagingService.sendToggleTodoAsync(todoId, "1"); // Default user ID
                logger.info("📤 Toggle operation queued for async processing");
                return true;
            } catch (Exception e) {
                logger.log(Level.SEVERE, "❌ Failed to queue toggle operation", e);
            }
        }
        
        return false;
    }
    
    private boolean handleDeleteTodoFallback(Long todoId) {
        logger.warning("🚨 Circuit breaker open - queuing delete operation for async processing");
        
        if (asyncProcessingEnabled && messagingService != null) {
            try {
                messagingService.sendDeleteTodoAsync(todoId, "1"); // Default user ID
                logger.info("📤 Delete operation queued for async processing");
                return true;
            } catch (Exception e) {
                logger.log(Level.SEVERE, "❌ Failed to queue delete operation", e);
            }
        }
        
        return false;
    }
    
    /**
     * Map ResultSet to Todo object
     */
    private Todo mapResultSetToTodo(ResultSet rs) throws SQLException {
        Todo todo = new Todo();
        todo.setTodoId(rs.getLong("todo_id"));
        todo.setTitle(rs.getString("title"));
        todo.setDescription(rs.getString("description"));
        todo.setCompleted(rs.getInt("completed") == 1);
        todo.setPriority(rs.getString("priority"));
        todo.setCreatedDate(rs.getTimestamp("created_date"));
        todo.setUpdatedDate(rs.getTimestamp("updated_date"));
        todo.setDueDate(rs.getDate("due_date"));
        todo.setUserId(rs.getLong("user_id"));
        return todo;
    }
    
    /**
     * Get resilience status
     */
    public DatabaseResilienceManager.ResilienceStatus getResilienceStatus() {
        return resilienceManager.getStatus();
    }
    
    /**
     * Perform manual health check
     */
    public boolean performHealthCheck() {
        return resilienceManager.performHealthCheck();
    }
    
    /**
     * Reset circuit breaker manually
     */
    public void resetCircuitBreaker() {
        resilienceManager.resetCircuitBreaker();
    }
}
