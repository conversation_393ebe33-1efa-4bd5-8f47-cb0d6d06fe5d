# 🔧 Deploy SavvySpend with JNDI Configuration

## 🎯 Quick Fix Summary

**Problem**: SavvySpend application fails with "missing table [budgets]" error
**Root Cause**: Application uses direct JDBC connection, but should use WebSphere JNDI
**Solution**: Configure application to use existing JNDI datasource `jdbc/OracleDS`

## ✅ Prerequisites Verified

✅ **Database Tables**: All required tables exist in XEPDB1 database (SYSTEM schema)
✅ **JNDI DataSource**: `jdbc/OracleDS` exists and points to XEPDB1
✅ **WebSphere**: Server is running and accessible

## 🔧 Quick Fix Steps

### **Step 1: Verify JNDI DataSource**
The existing JNDI datasource is correctly configured:
- **JNDI Name**: `jdbc/OracleDS`
- **URL**: `***************************************`
- **Database**: XEPDB1 (where all tables exist)
- **Schema**: SYSTEM

### **Step 2: Application Configuration Change**

**Current Configuration (FAILING):**
```properties
# Direct JDBC (CAUSES ERROR)
spring.datasource.url=***********************************
spring.datasource.username=system
spring.datasource.password=SavvySpend123
```

**Required Configuration (WORKING):**
```properties
# JNDI Configuration (FIXES ERROR)
spring.datasource.jndi-name=jdbc/OracleDS

# Remove direct JDBC properties
# spring.datasource.url=...
# spring.datasource.username=...
# spring.datasource.password=...
```

### **Step 3: Manual Fix via WebSphere Admin Console**

Since modifying the deployed application is complex, here's the **simplest approach**:

1. **Access WebSphere Admin Console**: https://localhost:9043/ibm/console

2. **Navigate to Applications**:
   - Applications → Application Types → WebSphere enterprise applications
   - Find `savvyspend-pro`
   - Click on it

3. **Update Application Configuration**:
   - Look for application properties or configuration
   - Add JNDI configuration if possible
   - Or redeploy with corrected configuration

### **Step 4: Alternative - Test JNDI Connection**

Let's first test if the JNDI connection works by testing it:

```bash
# Test JNDI datasource connection
docker exec appserver /opt/IBM/WebSphere/AppServer/bin/wsadmin.sh -lang jython -c "
import javax.naming as naming
ctx = naming.InitialContext()
ds = ctx.lookup('jdbc/OracleDS')
conn = ds.getConnection()
print('✅ JNDI Connection successful!')
conn.close()
"
```

## 🎯 Expected Results

### **Before Fix:**
```
❌ Schema-validation: missing table [budgets]
❌ Direct JDBC connection to XE database
❌ Tables exist in XEPDB1, but app connects to XE
❌ Application fails to start
```

### **After Fix:**
```
✅ JNDI connection to jdbc/OracleDS
✅ Database connection to XEPDB1 (where tables exist)
✅ All tables found: BUDGETS, USERS, CATEGORIES, EXPENSES, GOALS
✅ SavvySpend application starts successfully
✅ Application accessible at: http://localhost:9080/savvyspend/
```

## 🚨 If Manual Fix is Too Complex

### **Alternative: Redeploy with Correct Configuration**

If modifying the existing application is too complex, we can:

1. **Create new WAR file** with JNDI configuration
2. **Undeploy current application**
3. **Deploy new JNDI-enabled application**

This would require:
- Access to SavvySpend source code
- Modify application.properties to use JNDI
- Rebuild and redeploy WAR file

## 📋 Verification Checklist

After applying the fix:

- [ ] WebSphere server restarted
- [ ] No "missing table [budgets]" error in SystemOut.log
- [ ] SavvySpend application shows as "Started" in admin console
- [ ] Application accessible at http://localhost:9080/savvyspend/
- [ ] Database operations work correctly

## 🎉 Summary

The fix is simple in concept:
1. **Change application configuration** from direct JDBC to JNDI
2. **Use existing JNDI datasource** `jdbc/OracleDS`
3. **Tables already exist** in the correct database (XEPDB1)

The main challenge is **modifying the deployed application configuration** to use JNDI instead of direct JDBC connection.

---

**🔧 The quickest fix would be to modify the application.properties file within the deployed WAR to use `spring.datasource.jndi-name=jdbc/OracleDS` instead of the direct JDBC properties.**
