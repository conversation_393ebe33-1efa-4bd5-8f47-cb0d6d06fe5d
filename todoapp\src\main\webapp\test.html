<!DOCTYPE html>
<html>
<head>
    <title>Todo Test</title>
</head>
<body>
    <h1>Todo Test Page</h1>
    
    <form id="testForm">
        <input type="text" id="title" name="title" value="Test Todo" required>
        <textarea id="description" name="description">Test Description</textarea>
        <select id="priority" name="priority">
            <option value="LOW">Low</option>
            <option value="MEDIUM" selected>Medium</option>
            <option value="HIGH">High</option>
        </select>
        <button type="button" onclick="testSave()">Test Save</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        function testSave() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            formData.append('action', 'create');
            
            console.log('Sending request to: /todo-list-app/todos');
            
            fetch('/todo-list-app/todos', {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(function(data) {
                console.log('Response data:', data);
                document.getElementById('result').innerHTML = '<pre>' + data + '</pre>';
            })
            .catch(function(error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = '<pre>Error: ' + error + '</pre>';
            });
        }
    </script>
</body>
</html>
