-- Fix Payment Methods for Demo User
-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Enable output for debugging
SET SERVEROUTPUT ON;

-- Get demo user ID
VARIABLE demo_user_id NUMBER;
BEGIN
    SELECT id INTO :demo_user_id FROM users WHERE username = 'demo';
    DBMS_OUTPUT.PUT_LINE('Demo user ID: ' || :demo_user_id);
END;
/

-- Show current payment methods
SELECT 'Current payment methods for demo user:' AS info FROM dual;
SELECT DISTINCT payment_method FROM expenses WHERE user_id = :demo_user_id;

-- Update invalid payment methods to valid ones
-- Based on the HTML form, valid options are:
-- Cash, Credit Card, Debit Card, Bank Transfer, Digital Wallet, Check, Other

-- Update "Bank Transfer" to "Digital Wallet" (since Bank Transfer seems to be causing issues)
UPDATE expenses 
SET payment_method = 'Digital Wallet'
WHERE user_id = :demo_user_id 
AND payment_method = 'Bank Transfer';

-- Verify the changes
SELECT 'Updated payment methods:' AS info FROM dual;
SELECT DISTINCT payment_method FROM expenses WHERE user_id = :demo_user_id;

-- Show all expenses for demo user with updated payment methods
SELECT 'All expenses for demo user:' AS info FROM dual;
SELECT 
    id,
    amount,
    description,
    TO_CHAR(expense_date, 'YYYY-MM-DD') AS expense_date,
    payment_method,
    category_id
FROM expenses 
WHERE user_id = :demo_user_id
ORDER BY expense_date DESC;

COMMIT;

SELECT 'Payment methods fixed successfully!' AS status FROM dual;

EXIT;
