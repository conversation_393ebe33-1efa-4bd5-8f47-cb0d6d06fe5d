-- Connect to SavvySpend schema
CONNECT savvyspend/SavvySpend123@//localhost:1521/XEPDB1;

-- Check the actual users table structure
DESCRIBE users;

-- List all columns in users table
SELECT column_name, data_type, data_length, nullable
FROM user_tab_columns 
WHERE table_name = 'USERS'
ORDER BY column_id;

-- Check if demo user exists and what fields are available
SELECT * FROM users WHERE username = 'demo';

-- Check all users to see what data is available
SELECT * FROM users ORDER BY user_id;

-- Check if there are any password-related columns
SELECT column_name 
FROM user_tab_columns 
WHERE table_name = 'USERS' 
AND (UPPER(column_name) LIKE '%PASSWORD%' OR UPPER(column_name) LIKE '%HASH%');

EXIT;
