-- =============================================================================
-- SAVVYSPEND APPLICATION - XE CONTAINER DATABASE TABLES
-- =============================================================================
-- Creates the SavvySpend tables in the XE container database (not XEPDB1)
-- The application connects to ***********************************
-- =============================================================================

SET SERVEROUTPUT ON SIZE 1000000
SET ECHO ON

PROMPT =============================================================================;
PROMPT CREATING SAVVYSPEND TABLES IN XE CONTAINER DATABASE
PROMPT =============================================================================;

-- Connect to the XE container database as SYSTEM user
CONNECT system/SavvySpend123@XE;

-- Create budgets table in XE container database (THE MISSING TABLE!)
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'BUDGETS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE budgets (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            amount NUMBER(10,2) NOT NULL,
            spent NUMBER(10,2) DEFAULT 0,
            category_id NUMBER(19),
            user_id NUMBER(19),
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active NUMBER(1) DEFAULT 1,
            CONSTRAINT pk_budgets PRIMARY KEY (id),
            CONSTRAINT chk_budgets_amount CHECK (amount > 0),
            CONSTRAINT chk_budgets_spent CHECK (spent >= 0),
            CONSTRAINT chk_budgets_active CHECK (active IN (0, 1)),
            CONSTRAINT chk_budgets_dates CHECK (end_date > start_date)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table budgets created in XE container database - THIS FIXES THE ERROR!');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table budgets already exists in XE container database');
    END IF;
END;
/

-- Create users table in XE container database
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'USERS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE users (
            id NUMBER(19) NOT NULL,
            username VARCHAR2(50) NOT NULL UNIQUE,
            password VARCHAR2(255) NOT NULL,
            email VARCHAR2(100) NOT NULL UNIQUE,
            first_name VARCHAR2(50),
            last_name VARCHAR2(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active NUMBER(1) DEFAULT 1,
            CONSTRAINT pk_users PRIMARY KEY (id),
            CONSTRAINT chk_users_active CHECK (active IN (0, 1))
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table users created in XE container database');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table users already exists in XE container database');
    END IF;
END;
/

-- Create categories table in XE container database
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'CATEGORIES';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE categories (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            description VARCHAR2(255),
            color VARCHAR2(7) DEFAULT ''#007bff'',
            user_id NUMBER(19),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT pk_categories PRIMARY KEY (id)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table categories created in XE container database');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table categories already exists in XE container database');
    END IF;
END;
/

-- Create expenses table in XE container database
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'EXPENSES';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE expenses (
            id NUMBER(19) NOT NULL,
            amount NUMBER(10,2) NOT NULL,
            description VARCHAR2(255) NOT NULL,
            expense_date DATE NOT NULL,
            category_id NUMBER(19),
            user_id NUMBER(19),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT pk_expenses PRIMARY KEY (id),
            CONSTRAINT chk_expenses_amount CHECK (amount > 0)
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table expenses created in XE container database');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table expenses already exists in XE container database');
    END IF;
END;
/

-- Create goals table in XE container database
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM user_tables WHERE table_name = 'GOALS';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE goals (
            id NUMBER(19) NOT NULL,
            name VARCHAR2(100) NOT NULL,
            target_amount NUMBER(10,2) NOT NULL,
            current_amount NUMBER(10,2) DEFAULT 0,
            target_date DATE,
            user_id NUMBER(19),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            achieved NUMBER(1) DEFAULT 0,
            CONSTRAINT pk_goals PRIMARY KEY (id),
            CONSTRAINT chk_goals_target_amount CHECK (target_amount > 0),
            CONSTRAINT chk_goals_current_amount CHECK (current_amount >= 0),
            CONSTRAINT chk_goals_achieved CHECK (achieved IN (0, 1))
        )';
        DBMS_OUTPUT.PUT_LINE('✅ Table goals created in XE container database');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Table goals already exists in XE container database');
    END IF;
END;
/

-- Create sequences for auto-incrementing IDs
DECLARE
    seq_count NUMBER;
BEGIN
    -- Budgets sequence (most important)
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'BUDGETS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE budgets_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence budgets_seq created in XE container database');
    END IF;
    
    -- Users sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'USERS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE users_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence users_seq created in XE container database');
    END IF;
    
    -- Categories sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'CATEGORIES_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE categories_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence categories_seq created in XE container database');
    END IF;
    
    -- Expenses sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'EXPENSES_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE expenses_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence expenses_seq created in XE container database');
    END IF;
    
    -- Goals sequence
    SELECT COUNT(*) INTO seq_count FROM user_sequences WHERE sequence_name = 'GOALS_SEQ';
    IF seq_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE goals_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
        DBMS_OUTPUT.PUT_LINE('✅ Sequence goals_seq created in XE container database');
    END IF;
END;
/

-- Insert minimal sample data to make the application work
DECLARE
    data_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO data_count FROM budgets;
    
    IF data_count = 0 THEN
        -- Insert a sample budget record to satisfy the schema validation
        INSERT INTO budgets (id, name, amount, spent, start_date, end_date) VALUES 
        (1, 'Sample Budget', 1000.00, 0, SYSDATE, SYSDATE + 30);
        
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('✅ Sample budget record inserted in XE container database');
        DBMS_OUTPUT.PUT_LINE('   - This satisfies the schema validation requirement');
    ELSE
        DBMS_OUTPUT.PUT_LINE('ℹ️  Budget data already exists in XE container database');
    END IF;
END;
/

PROMPT;
PROMPT =============================================================================;
PROMPT SAVVYSPEND TABLES CREATED IN XE CONTAINER DATABASE!;
PROMPT =============================================================================;
PROMPT;
PROMPT ✅ The missing 'budgets' table has been created in the correct database (XE);
PROMPT ✅ The SavvySpend application should now start successfully!;
PROMPT ✅ Application connects to: ***********************************;
PROMPT ✅ Tables created in: XE container database (SYSTEM schema);
PROMPT;
PROMPT =============================================================================;
